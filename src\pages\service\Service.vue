<script setup lang="ts">
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { useRoute } from 'vue-router';
import { router } from '../../router';
import { Warning, SwitchButton, User, Menu, Avatar, Promotion, BellFilled, WarningFilled, List } from '@element-plus/icons-vue';
import crypto from 'crypto-js';
import requset from '../../api';

const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

function menuGoRoute(name: string) {
    if (route.name === name) {
        return;
    }
    router.push({
        name: name
    });
}


</script>

<template>
    <el-aside class="admin-aside">
        <el-menu class="admin-menu" :default-active="route.name" text-color="#fff" background-color="#333744"
            v-if="mainStore.queryMenuPermission(mainStore.menuList, 3000)">
            <el-menu-item index="tradeSearch" @click="menuGoRoute('tradeSearch')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 3002)">
                <el-icon>
                    <List />
                </el-icon>
                <span>订单号查询</span>
            </el-menu-item>
            <el-menu-item index="unbindChinaid" @click="menuGoRoute('unbindChinaid')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 3001)">
                <el-icon>
                    <Avatar />
                </el-icon>
                <span>身份证解绑</span>
            </el-menu-item>
            <el-menu-item index="clearTicket" @click="menuGoRoute('clearTicket')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 3003)">
                <el-icon>
                    <Avatar />
                </el-icon>
                <span>清除点券</span>
            </el-menu-item>
            <el-menu-item index="clearNobleMonth" @click="menuGoRoute('clearNobleMonth')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 3004)">
                <el-icon>
                    <Avatar />
                </el-icon>
                <span>清除贵族月卡</span>
            </el-menu-item>
        </el-menu>
    </el-aside>
    <el-main class="admin-content">
        <router-view v-if="mainStore.checkPermission(3000)"></router-view>
        <h2 v-else>权限不足, 请联系管理员!</h2>
    </el-main>
</template>

<style lang="less" scoped>

</style>