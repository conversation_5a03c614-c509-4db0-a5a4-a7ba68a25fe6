<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ArrowRight, House } from '@element-plus/icons-vue';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { router } from '../../router';
import { CoopGameInfo, Forbid, HackCheckRecord, IpInfo, IpQuery, OldNicknameItem, RecentLoginItem, TalkMsg, UserAvatar, UserTaskRecord, VsGameInfo } from '../../interface';
import axios from "axios";
import { ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';

const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const tabIndex = ref('base');
const uid = ref(0);
if (route.query && route.query.uid) {
    uid.value = parseInt(route.query.uid.toString());
    if (!uid.value || uid.value <= 0) {
        router.push({
            name: 'home'
        });
        mainStore.globalMessageTip('用户ID传入异常', 3);
    }
}

const userInfoFetchFlag = ref(false);
const userInfo = reactive({
    basicInfo: {
        nickname: '',
        account: '',
        user_id: '',
        account_state: '',
        phone: '',
        verify_state: '',
        register_time: '',
        register_ip: '',
        register_area: '',
        last_login_time: '',
        last_login_ip: '',
        last_login_area: '',
        handler_info: '',
        noble_expire_time: '',
        noble_id: 0,
        vip_level: 0,
        avatar_using: 0,
        icon_url: '',
        member_expire_time: '',
        forbid_flag: false,
        ban_flag: false,
        report_black: false,
    },
    liveInfo: {
        live_url: '',
        handler_info: '',
        live_level: '',
        icon_url: '',
        live_state: 0
    },
    avatarList: [] as Array<UserAvatar>,
    recentLogin: [] as Array<RecentLoginItem>,
    oldNickname: [] as Array<OldNicknameItem>,
    forbidRecord: [] as Array<Forbid>,
    talkRecord: [] as Array<TalkMsg>,
    hackRecord: [] as Array<HackCheckRecord>,
    vsGameInfo: [] as Array<VsGameInfo>,
    coopGameInfo: [] as Array<CoopGameInfo>,
    ip_query: [] as Array<IpQuery>,
    ip_list: [] as Array<IpInfo>
});
const reoportPunishTime = [
    {
        id: 1,
        label: '1天'
    },
    {
        id: 2,
        label: '3天'
    },
    {
        id: 3,
        label: '7天'
    },
    {
        id: 4,
        label: '15天'
    },
    {
        id: 5,
        label: '30天'
    },
    {
        id: 6,
        label: '90天'
    },
    {
        id: 7,
        label: '180天'
    },
    {
        id: 8,
        label: '365天'
    },
    {
        id: 9,
        label: '3650天'
    },
];
const handleReportType = [
    {
        type_id: 2,
        type_name: '外挂或连发',
    },
    {
        type_id: 3,
        type_name: '恶意刷级',
    },
    {
        type_id: 7,
        type_name: '违规言论',
    },
    {
        type_id: 8,
        type_name: '其它',
    },
];
const forbidUserForm = reactive({
    handle_type: 2,
    punish_type: 1,
    punish_time: 1,
});
const banUserForm = reactive({
    handle_type: 2,
    punish_type: 2,
    punish_time: 1,
});


const addLiveRoomDialog = ref(false);
const addLiveRoomForm = reactive({
    live_url: '',
});
const addLiveRoomFormRef = ref<FormInstance>();
const addLiveRoomFormRules = reactive<FormRules>({
    live_url: [
        {
            required: true, message: '请输入直播链接', trigger: 'blur'
        }
    ],
});

getUserDetailInfo();

async function getUserDetailInfo() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        userInfoFetchFlag.value = false;
        await requset.getUserDetailInfo({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetUserID: uid.value,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                if (res.data) {
                    if (res.data.basicInfo) {
                        if (res.data.basicInfo.register_ip) {
                            const ipIndex = userInfo.ip_query.findIndex(item => item.query == res.data.basicInfo.register_ip);
                            if (ipIndex === -1) {
                                userInfo.ip_query.push({
                                    query: res.data.basicInfo.register_ip,
                                    fields: 'status,message,country,countryCode,region,regionName,city,district,isp,org,as,mobile,proxy,query',
                                    lang: 'zh-CN'
                                });
                            }
                        }
                        if (res.data.basicInfo.last_login_ip) {
                            const ipIndex = userInfo.ip_query.findIndex(item => item.query == res.data.basicInfo.last_login_ip);
                            if (ipIndex === -1) {
                                userInfo.ip_query.push({
                                    query: res.data.basicInfo.last_login_ip,
                                    fields: 'status,message,country,countryCode,region,regionName,city,district,isp,org,as,mobile,proxy,query',
                                    lang: 'zh-CN'
                                });
                            }
                        }
                        if (res.data.recentLogin) {
                            res.data.recentLogin.forEach((item: { login_ip: any; }) => {
                                const ipIndex = userInfo.ip_query.findIndex(item2 => item2.query == item.login_ip);
                                if (ipIndex === -1) {
                                    userInfo.ip_query.push({
                                        query: item.login_ip,
                                        fields: 'status,message,country,countryCode,region,regionName,city,district,isp,org,as,mobile,proxy,query',
                                        lang: 'zh-CN'
                                    });
                                }
                            });
                        }
                        userInfo.basicInfo.report_black = res.data.basicInfo.report_black;
                    }
                    if (userInfo.ip_query.length > 0) {
                        const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
                        const ipList = userInfo.ip_query.map(item => item.query).join(',');
                        requset.queryBatchIpInfo({
                            userID: userID,
                            identityToken: identityToken,
                            authorityID: authorityID,
                            ipList: ipList,
                            token: token
                        }).then(res => {
                            if (res.code === 0) {
                                if (res.data && res.data.ipInfoList) {
                                    userInfo.ip_list = res.data.ipInfoList;
                                }
                            }
                        }).catch(err => {
                            console.log(err);

                        });
                        // await axios.post('//ip-api.com/batch', JSON.stringify(userInfo.ip_query)).then(res => {
                        //     userInfo.ip_list = res.data;
                        // }).catch(err => {
                        //     console.log(err);
                        // });
                    }
                    userInfo.basicInfo = res.data.basicInfo;
                    userInfo.recentLogin = res.data.recentLogin;
                    userInfo.oldNickname = res.data.oldNickname;
                    userInfo.avatarList = res.data.avatarList;
                    userInfo.talkRecord = res.data.talkRecord;
                    userInfo.forbidRecord = res.data.forbidRecord;
                    userInfo.hackRecord = res.data.hackRecord;
                    userInfo.liveInfo = res.data.liveInfo;
                    userInfo.vsGameInfo = res.data.vsGameInfo;
                    userInfo.coopGameInfo = res.data.coopGameInfo;
                    userInfoFetchFlag.value = true;
                }
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该用户不存在', 3);
                            router.push({
                                name: 'home'
                            });
                            break;
                        case 6:
                            mainStore.globalMessageTip('该用户的背包不存在，请联系周玉华', 3);
                            router.push({
                                name: 'home'
                            });
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function banUserSubmit() {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${banUserForm.handle_type}${banUserForm.punish_type}${banUserForm.punish_time}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.banUser({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_userid: uid.value,
            handleType: banUserForm.handle_type,
            punishType: banUserForm.punish_type,
            punishTime: banUserForm.punish_time,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
                await getUserDetailInfo();
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function forbidUserSubmit() {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${forbidUserForm.handle_type}${forbidUserForm.punish_type}${forbidUserForm.punish_time}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.banUser({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_userid: uid.value,
            handleType: forbidUserForm.handle_type,
            punishType: forbidUserForm.punish_type,
            punishTime: forbidUserForm.punish_time,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
                await getUserDetailInfo();
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function unfreezeUserSubmit(type: number) {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${type}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.unfreezeUser({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_userid: uid.value,
            unfreezeType: type,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
                await getUserDetailInfo();
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function clearUserGameScore(game_id: number) {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0 || game_id < 0) {
        return;
    }
    ElMessageBox.confirm(`确定要清除 ${mainStore.getGameName(game_id)} 的战绩?`, '刷级处理', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${game_id}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.clearUserGameScore({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid.value,
                gameID: game_id,
                token: token
            }).then(async res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    await getUserDetailInfo();
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

async function clearUserNickname(flag: number) {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }


    ElMessageBox.confirm(`确定用户昵称: ${userInfo.basicInfo.nickname} 违规?`, '违规昵称', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${flag}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.clearUserNickname({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid.value,
                handleType: flag,
                targetNickname: userInfo.basicInfo.nickname,
                token: token
            }).then(async res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    await getUserDetailInfo();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('用户不存在', 3);
                                await getUserDetailInfo();
                                break;
                            case 6:
                                mainStore.globalMessageTip('该用户昵称已更新', 3);
                                await getUserDetailInfo();
                                break;
                            case 7:
                                mainStore.globalMessageTip('昵称没有违规，不需要处理', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

async function clearUserPHone() {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    ElMessageBox.confirm(`确定解除用户: ${userInfo.basicInfo.nickname} 的手机号?`, '解除手机号', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.clearUserPhone({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid.value,
                token: token
            }).then(async res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    await getUserDetailInfo();
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

async function addUserLiveRoomSubmit(formEl: FormInstance | undefined) {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${requset.ADMIN_TOKEN_KEY}`).toString();
            if (userID && identityToken) {
                mainStore.globalLoading(true);
                await requset.addUserLiveRoom({
                    userID: userID,
                    identityToken: identityToken,
                    authorityID: authorityID,
                    targetUserID: uid.value,
                    live_url: addLiveRoomForm.live_url,
                    token: token
                }).then(async res => {
                    if (res.code === 0) {
                        mainStore.globalMessageTip('处理成功', 0);
                        await getUserDetailInfo();
                    } else {
                        if (!mainStore.dealResponseErrInfo(res.code)) {
                            switch (res.code) {
                                case 5:
                                    mainStore.globalMessageTip('首次添加需要输入链接', 3);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }).catch(err => {
                    mainStore.dealResponseErrInfo(-1);
                });
                mainStore.globalLoading(false);
                addLiveRoomDialog.value = false;
            } else {
                mainStore.dealResponseErrInfo(4);
            }
        } else {
            return;
        }
    });

}

async function deleteUserLiveRoom() {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    ElMessageBox.confirm(`确定要删除该用户的直播间?`, '删除直播间', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.deleteUserLiveRoom({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid.value,
                token: token
            }).then(async res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    await getUserDetailInfo();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该用户没有直播间', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

async function clearUserAvatar(avatar_id: number, flag: number) {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0 || !avatar_id) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${avatar_id}${flag}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.clearUserAvatar({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetUserID: uid.value,
            targetAvatarID: avatar_id,
            handleType: flag,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
                await getUserDetailInfo();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('用户背包不存在，请联系周玉华', 3);
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function setUserVerifyMode(mode: number) {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${mode}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.setUserVerifyMode({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetUserID: uid.value,
            verifyMode: mode,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
                await getUserDetailInfo();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('用户背包不存在，请联系周玉华', 3);
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function getIpInfo(ip: string) {
    if (ip) {
        const index = userInfo.ip_list.findIndex(item => item.query == ip);
        if (index >= 0) {
            return `${userInfo.ip_list[index].country}${userInfo.ip_list[index].regionName}${userInfo.ip_list[index].city} `;
        }
    }
}

function getIspName(isp: string) {
    switch (isp) {
        case 'Chinanet':
            return '中国电信';
        case 'Cmcc':
            return '中国移动';
        case 'Chinaunicom':
            return '中国联通';
        default:
            return isp;
    }
}

async function addUserReportBlack() {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    ElMessageBox.confirm(`确定要添加到举报黑名单?`, '举报黑名单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.addUserReportBlack({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid.value,
                token: token
            }).then(async res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    await getUserDetailInfo();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该用户已经存在黑名单里', 3);
                                await getUserDetailInfo();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

async function deleteUserReportBlack() {
    if (!userInfoFetchFlag.value) {
        return mainStore.globalMessageTip('用户数据正在同步...', 1);
    }
    if (!uid.value || uid.value <= 0) {
        return;
    }
    ElMessageBox.confirm(`确定要从举报黑名单中移除?`, '举报黑名单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid.value}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.removeUserReportBlack({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid.value,
                token: token
            }).then(async res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    await getUserDetailInfo();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该用户不在黑名单中', 3);
                                await getUserDetailInfo();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

</script>

<template>
    <el-aside class="admin-aside">
    </el-aside>
    <el-main class="admin-content">
        <div class="user-detai" v-if="mainStore.checkPermission(100)">
            <el-space direction="vertical" style="width: 100%" fill>
                <el-breadcrumb :separator-icon="ArrowRight">
                    <el-breadcrumb-item :to="{
                        name: 'outsourcing'
                    }">
                        外协管理
                    </el-breadcrumb-item>
                    <el-breadcrumb-item>账户详情</el-breadcrumb-item>
                </el-breadcrumb>
                <el-card class="content" v-loading="loading">
                    <el-space direction="vertical" style="width: 100%" fill :size="32">
                        <el-space direction="vertical" style="width: 100%" fill size="large">
                            <el-row align="middle">
                                <el-col :span="1"></el-col>
                                <el-col :span="22">
                                    <el-row align="middle">
                                        <el-space wrap>
                                            <el-space direction="horizontal">
                                                <span>昵称: {{ userInfo.basicInfo.nickname }}</span>
                                                <el-link type="primary" :underline="false"
                                                    @click="mainStore.copyContent(userInfo.basicInfo.nickname)"
                                                    v-if="userInfo.basicInfo.nickname">复制</el-link>
                                            </el-space>
                                            <el-space direction="horizontal">
                                                <span>账户: {{ userInfo.basicInfo.account }}</span>
                                                <el-link type="primary" :underline="false"
                                                    @click="mainStore.copyContent(userInfo.basicInfo.account)"
                                                    v-if="userInfo.basicInfo.account">复制</el-link>
                                            </el-space>
                                            <el-space direction="horizontal">
                                                <span>账户ID: {{ userInfo.basicInfo.user_id }}</span>
                                                <el-link type="primary"
                                                    @click="mainStore.copyContent(userInfo.basicInfo.user_id)"
                                                    :underline="false" v-if="userInfo.basicInfo.user_id">复制</el-link>
                                            </el-space>
                                        </el-space>
                                    </el-row>
                                </el-col>
                                <el-col :span="1"></el-col>
                            </el-row>
                            <el-row align="middle">
                                <el-col :span="1"></el-col>
                                <el-col :span="22">
                                    <el-row align="middle">
                                        <el-space direction="horizontal" style="width: 100%" wrap>
                                            <span>曾用昵称: </span>
                                            <span v-if="userInfo.oldNickname.length === 0">无</span>
                                            <el-tag type="info" v-for="(item, index) in userInfo.oldNickname"
                                                :key="item.id">{{ item.nickname }}
                                            </el-tag>
                                        </el-space>
                                    </el-row>
                                </el-col>
                                <el-col :span="1"></el-col>
                            </el-row>
                        </el-space>
                        <el-tabs type="border-card" v-model="tabIndex">
                            <el-tab-pane label="基础信息" name="base">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-space :size="32" direction="vertical" style="width: 100%;" fill>
                                            <el-row>
                                                <el-col :span="3">状态: </el-col>
                                                <el-col :span="21">{{ userInfo.basicInfo.account_state }}</el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="3">操作人: </el-col>
                                                <el-col :span="21">
                                                    <span>{{ userInfo.basicInfo.handler_info }}</span>
                                                </el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="3">手机号: </el-col>
                                                <el-col :span="21">
                                                    <el-row align="middle">
                                                        <el-space wrap>
                                                            <span v-if="userInfo.basicInfo.phone">{{
                                                                userInfo.basicInfo.phone
                                                            }} {{ mainStore.isVtPhone(userInfo.basicInfo.phone) ?
    '(虚拟号段)' : '' }} </span>
                                                            <span v-else>无</span>
                                                            <el-link type="danger" :underline="false"
                                                                v-if="userInfo.basicInfo.phone && mainStore.checkPermission(111)" @click="clearUserPHone()">
                                                                解除绑定</el-link>
                                                            <el-button type="danger" v-if="userInfo.basicInfo.phone && mainStore.checkPermission(112)"
                                                                size="large" @click="setUserVerifyMode(2)">
                                                                强制短信验证</el-button>
                                                        </el-space>

                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                            <el-row v-if="mainStore.checkPermission(112)">
                                                <el-col :span="3">验证状态: </el-col>
                                                <el-col :span="21">{{ userInfo.basicInfo.verify_state }}</el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="3">注册日期: </el-col>
                                                <el-col :span="21">
                                                    <el-row align="middle">
                                                        <el-space wrap>
                                                            <span>{{ userInfo.basicInfo.register_time }}</span>
                                                            <el-link type="primary" :underline="false"
                                                                @click="mainStore.copyContent(userInfo.basicInfo.register_ip)">
                                                                {{ userInfo.basicInfo.register_ip }}
                                                            </el-link>
                                                            <span v-if="userInfo.basicInfo.register_ip">({{
                                                                getIpInfo(userInfo.basicInfo.register_ip)
                                                            }})</span>
                                                        </el-space>
                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="3">最后登录: </el-col>
                                                <el-col :span="21">
                                                    <el-row align="middle">
                                                        <el-space wrap>
                                                            <span>{{ userInfo.basicInfo.last_login_time }}</span>
                                                            <el-link type="primary" :underline="false"
                                                                @click="mainStore.copyContent(userInfo.basicInfo.last_login_ip)">
                                                                {{ userInfo.basicInfo.last_login_ip }}
                                                            </el-link>
                                                            <span v-if="userInfo.basicInfo.last_login_ip">({{
                                                                getIpInfo(userInfo.basicInfo.last_login_ip)
                                                            }})</span>
                                                        </el-space>
                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="3">最近登录: </el-col>
                                                <el-col :span="21">
                                                    <el-table :data="userInfo.recentLogin" style="width: 100%" border>
                                                        <el-table-column prop="login_time" label="登陆时间" />
                                                        <el-table-column label="登陆设备">
                                                            <template #default="scope">
                                                                <span v-if="scope.row.dev_type === 1">PC端</span>
                                                                <span v-else-if="scope.row.dev_type === 2">手机端</span>
                                                                <span v-else>未定义</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="login_ip" label="登录IP">
                                                        </el-table-column>
                                                        <el-table-column label="地区">
                                                            <template #default="scope">
                                                                <span>{{ getIpInfo(scope.row.login_ip) }}</span>
                                                            </template>
                                                        </el-table-column>
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                        </el-space>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                            </el-tab-pane>
                            <el-tab-pane label="违禁处理" name="forbid">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-space :size="32" direction="vertical" style="width: 100%;" fill>
                                            <el-row>
                                                <el-col :span="2">状态: </el-col>
                                                <el-col :span="22">{{ userInfo.basicInfo.account_state }}</el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="2">昵称: </el-col>
                                                <el-col :span="22">
                                                    <el-space>
                                                        <span>{{ userInfo.basicInfo.nickname }}</span>
                                                        <el-button type="danger" size="large"
                                                            @click="clearUserNickname(0)">违规昵称
                                                        </el-button>
                                                        <el-button type="danger" size="large"
                                                            @click="clearUserNickname(1)">违规昵称(补改名卡)
                                                        </el-button>
                                                    </el-space>
                                                </el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="2">举报黑名单: </el-col>
                                                <el-col :span="22">
                                                    <el-row align="middle">
                                                        <el-col :span="4">
                                                            <el-button type="danger" size="large"
                                                                @click="addUserReportBlack()"
                                                                v-if="!userInfo.basicInfo.report_black">加入黑名单
                                                            </el-button>
                                                            <el-button type="info" size="large"
                                                                @click="deleteUserReportBlack()"
                                                                v-if="userInfo.basicInfo.report_black">移除黑名单
                                                            </el-button>
                                                        </el-col>
                                                    </el-row>
                                                </el-col>

                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="2">禁言: </el-col>
                                                <el-col :span="22">
                                                    <el-row align="middle">
                                                        <el-col :span="4">
                                                            <el-select v-model="forbidUserForm.punish_time"
                                                                placeholder="请选择" size="large" style="width: 80%;"
                                                                :disabled="userInfo.basicInfo.forbid_flag">
                                                                <el-option v-for="(item, index) in reoportPunishTime"
                                                                    :key="item.id" :label="item.label" :value="item.id" />
                                                            </el-select>
                                                        </el-col>
                                                        <el-col :span="4">
                                                            <el-select v-model="banUserForm.handle_type" placeholder="请选择"
                                                                size="large" style="width: 80%;"
                                                                :disabled="userInfo.basicInfo.forbid_flag">
                                                                <el-option v-for="(item, index) in handleReportType"
                                                                    :key="item.type_id" :label="item.type_name"
                                                                    :value="item.type_id" />
                                                            </el-select>
                                                        </el-col>
                                                        <el-col :span="4">
                                                            <el-button type="danger" v-if="!userInfo.basicInfo.forbid_flag"
                                                                size="large" @click="forbidUserSubmit()">
                                                                禁言处理</el-button>
                                                            <el-button type="info" v-else size="large"
                                                                @click="unfreezeUserSubmit(1)">解除禁言</el-button>
                                                        </el-col>
                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="2">封号: </el-col>
                                                <el-col :span="22">
                                                    <el-row align="middle">
                                                        <el-col :span="4">
                                                            <el-select v-model="banUserForm.punish_time" placeholder="请选择"
                                                                size="large" style="width: 80%;"
                                                                :disabled="userInfo.basicInfo.ban_flag">
                                                                <el-option v-for="(item, index) in reoportPunishTime"
                                                                    :key="item.id" :label="item.label" :value="item.id" />
                                                            </el-select>
                                                        </el-col>
                                                        <el-col :span="4">
                                                            <el-select v-model="banUserForm.handle_type" placeholder="请选择"
                                                                size="large" style="width: 80%;"
                                                                :disabled="userInfo.basicInfo.ban_flag">
                                                                <el-option v-for="(item, index) in handleReportType"
                                                                    :key="item.type_id" :label="item.type_name"
                                                                    :value="item.type_id" />
                                                            </el-select>
                                                        </el-col>
                                                        <el-col :span="4">
                                                            <el-button type="danger" v-if="!userInfo.basicInfo.ban_flag"
                                                                size="large" @click="banUserSubmit()">
                                                                封号处理</el-button>
                                                            <el-button type="info" v-else size="large"
                                                                @click="unfreezeUserSubmit(2)">解除封号</el-button>
                                                        </el-col>
                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">封禁记录: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="userInfo.forbidRecord" style="width: 100%" border>
                                                        <el-table-column prop="nickname" label="玩家昵称" />
                                                        <el-table-column prop="user_id" label="玩家ID">
                                                        </el-table-column>
                                                        <el-table-column prop="forbid_type" label="封禁类型" />
                                                        <el-table-column prop="start_time" label="开始时间" />
                                                        <el-table-column prop="end_time" label="解除时间" />
                                                        <el-table-column prop="handler" label="最后处理人" />
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">外挂检测: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="userInfo.hackRecord" style="width: 100%" border>
                                                        <el-table-column prop="id" label="序号" />
                                                        <el-table-column prop="ip_desc" label="描述" />
                                                        <el-table-column prop="check_time" label="检测时间">
                                                        </el-table-column>
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">聊天记录: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="userInfo.talkRecord" style="width: 100%" border>
                                                        <el-table-column prop="talk_time" label="聊天时间" />
                                                        <el-table-column label="游戏名称">
                                                            <template #default="scope">
                                                                <span>{{ mainStore.getGameName(scope.row.game_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="服务器">
                                                            <template #default="props">
                                                                <span>{{
                                                                    mainStore.getGameServerName(props.row.game_id,
                                                                        props.row.server_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="发言位置">
                                                            <template #default="props">
                                                                <span>{{ mainStore.getRoomName(props.row.room_id) }}
                                                                </span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="msg" label="聊天内容" />
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                        </el-space>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                            </el-tab-pane>
                            <el-tab-pane label="游戏信息" name="game">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-space :size="32" direction="vertical" style="width: 100%;" fill>

                                            <el-row>
                                                <el-col :span="2">对战游戏: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="userInfo.vsGameInfo" style="width: 100%" border>
                                                        <el-table-column label="游戏名称">
                                                            <template #default="scope">
                                                                <span>{{ mainStore.getGameName(scope.row.game_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="总场次">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.win + scope.row.lose + scope.row.draw
                                                                    + scope.row.escape
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="胜场次">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.win }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="负场次">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.lose }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="平局">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.draw }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="逃跑">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.escape }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="胜率">
                                                            <template #default="scope">
                                                                <span>{{ (scope.row.win /
                                                                    (scope.row.win + scope.row.lose + scope.row.draw +
                                                                        scope.row.escape)
                                                                    * 100).toFixed(2)
                                                                }}%</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="经验值">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.points }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="等级">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.level }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="操作">
                                                            <template #default="scope">
                                                                <el-button type="danger" size="large"
                                                                    @click="clearUserGameScore(scope.row.game_id)">刷级处理
                                                                </el-button>
                                                            </template>
                                                        </el-table-column>
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">闯关游戏: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="userInfo.coopGameInfo" style="width: 100%" border>
                                                        <el-table-column label="游戏名称">
                                                            <template #default="scope">
                                                                <span>{{ mainStore.getGameName(scope.row.game_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="单人通关次数">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.single_tg_num }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="多人通关次数">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.multi_tg_num }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="经验值">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.points }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="等级">
                                                            <template #default="scope">
                                                                <span>{{ scope.row.level }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="操作">
                                                            <template #default="scope">
                                                                <el-button type="danger" size="large"
                                                                    @click="clearUserGameScore(scope.row.game_id)">刷级处理
                                                                </el-button>
                                                            </template>
                                                        </el-table-column>
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">聊天记录: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="userInfo.talkRecord" style="width: 100%" border>
                                                        <el-table-column prop="talk_time" label="聊天时间" />
                                                        <el-table-column label="游戏名称">
                                                            <template #default="scope">
                                                                <span>{{ mainStore.getGameName(scope.row.game_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="服务器">
                                                            <template #default="props">
                                                                <span>{{
                                                                    mainStore.getGameServerName(props.row.game_id,
                                                                        props.row.server_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="发言位置">
                                                            <template #default="props">
                                                                <span>{{ mainStore.getRoomName(props.row.room_id) }}
                                                                </span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="msg" label="聊天内容" />
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                        </el-space>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                            </el-tab-pane>
                            <el-tab-pane label="其它信息" name="other">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-space :size="32" direction="vertical" style="width: 100%;" fill>
                                            <el-row align="middle">
                                                <el-col :span="2">直播间地址: </el-col>
                                                <el-col :span="22">
                                                    <el-space direction="horizontal" style="width: 100%">
                                                        <span>{{ userInfo.liveInfo.live_url }}</span>
                                                        <el-link type="success" :underline="false"
                                                            @click="addLiveRoomDialog = true"
                                                            v-if="mainStore.checkPermission(141)">更改</el-link>
                                                        <el-link type="danger" :underline="false"
                                                            @click="deleteUserLiveRoom()"
                                                            v-if="mainStore.checkPermission(141)">
                                                            删除</el-link>
                                                    </el-space>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">操作人信息: </el-col>
                                                <el-col :span="22">
                                                    <span>{{ userInfo.liveInfo.handler_info }}</span>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">TA的头像: </el-col>
                                                <el-col :span="22">
                                                    <el-row>
                                                        <el-space :wrap="true">
                                                            <div v-for="(item, index) in userInfo.avatarList"
                                                                :key="item.id">
                                                                <el-space direction="vertical" style="width: 100%;">
                                                                    <span
                                                                        v-if="item.id === userInfo.basicInfo.avatar_using">使用中</span>
                                                                    <span v-else>&nbsp;</span>
                                                                    <el-avatar :src="item.icon_url" :size="120" />
                                                                    <el-button type="danger" size="large"
                                                                        @click="clearUserAvatar(item.id, 0)">删除</el-button>
                                                                    <el-button type="danger" size="large"
                                                                        @click="clearUserAvatar(item.id, 1)">删除(补头像卡)</el-button>
                                                                </el-space>
                                                            </div>
                                                        </el-space>

                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                        </el-space>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                            </el-tab-pane>
                        </el-tabs>
                    </el-space>
                </el-card>
            </el-space>
        </div>
        <h2 v-else>权限不足, 请联系管理员!</h2>
    </el-main>

    <el-dialog v-model="addLiveRoomDialog" title="直播间配置">
        <el-form :model="addLiveRoomForm" ref="addLiveRoomFormRef" status-icon :rules="addLiveRoomFormRules">
            <el-form-item label="直播链接" prop="live_url">
                <el-input placeholder="请输入直播间地址" v-model="addLiveRoomForm.live_url" size="large" maxlength="255" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addUserLiveRoomSubmit(addLiveRoomFormRef)" size="large">确定</el-button>
                <el-button @click="addLiveRoomDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped></style>