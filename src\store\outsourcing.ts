import { defineStore } from "pinia";
import { useMainStore } from "./index";
import { router } from "../router";
import {
  AvatarReview,
  GameItem,
  GameSeries,
  IpUserSearchItem,
  NicknameReview,
  ReportItem,
  ServerNotice,
  UserSearchItem,
} from "../interface";

let fullLoading: any;

export const useOutsourceingStore = defineStore("outsourcing", {
  state: () => ({
    reportReviewNum: 0, //举报处理剩余任务数
    nicknameReviewNum: 0, //昵称审核剩余任务数
    avatarReviewNum: 0, //头像审核剩余任务数
    reportReviewProcessed: 0,
    nicknameReviewProcessed: 0,
    avatarReviewProcessed: 0,
    nicknameReviewList: [] as Array<NicknameReview>,
    avatarReviewList: [] as Array<AvatarReview>,
    reportReviewList: [] as Array<ReportItem>,
    reportGameSeriesOptions: [] as Array<{ value: number; label: string }>,
    reportGameListOptions: [] as Array<{ value: number; label: string }>,
    searchUserList: [] as Array<UserSearchItem>,
    searchIpUsers: [] as Array<IpUserSearchItem>,
    searchUserTotal: 0,
    serverNoticeList: [] as Array<ServerNotice>,
    serverNoticeTotal: 0,
  }),
  getters: {
    // getterReportReviewList(state) {
    //   state.reportReviewList.sort((a, b) => {
    //     if (a.bereport_userid > b.bereport_userid) {
    //       return b.bereport_userid - a.bereport_userid;
    //     }
    //   });
    // },
  },
  actions: {
    removeTheNicknameReview(targetID: number) {
      const index = this.nicknameReviewList.findIndex(
        (item) => item.user_id === targetID
      );
      if (index >= 0) {
        this.nicknameReviewList.splice(index, 1);
      }
    },
    removeTheAvatarReview(targetID: number) {
      const index = this.avatarReviewList.findIndex(
        (item) => item.id === targetID
      );
      if (index >= 0) {
        this.avatarReviewList.splice(index, 1);
      }
    },
    removeReportTask(reportID: number) {
      const index = this.reportReviewList.findIndex(
        (item) => item.id === reportID
      );
      if (index >= 0) {
        this.reportReviewList.splice(index, 1);
      }
    },
    removeCommonReport(bereporter: number) {
      this.reportReviewList = this.reportReviewList.filter(
        (item) => item.bereport_userid !== bereporter
      );
    },
  },
});
