<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { useInsideStore } from '../../store/inside';
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { router } from '../../router';
import { AbroadUserState, HackCheckItem } from '../../interface';

const mainStore = useMainStore();

const insideStore = useInsideStore();

const loading = ref(false);
const currentPage = ref(1);

const filterForm = reactive({
    sortType: 1, //1:按检测次数排序 2:按最后检测时间排序
    showForbid: 0
});
const filterFormRules = [
    {

    }
];


const hackCheck = reactive({
    total: 0,
    data: [] as Array<HackCheckItem>
});

getHackCheckList();

async function getHackCheckList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${filterForm.sortType}${filterForm.showForbid}${currentPage.value}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getHackCheckList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            currentPage: currentPage.value,
            sortType: filterForm.sortType,
            showForbid: filterForm.showForbid,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.hackList) {
                    hackCheck.total = res.data.total;
                    hackCheck.data = res.data.hackList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function changeShowForbid(val: number) {
    filterForm.showForbid = val;
    getHackCheckList();
}

function changeSortType(val: number) {
    filterForm.sortType = val;
    getHackCheckList();
}

async function currentPageChange(page: any) {
    currentPage.value = page;
    await getHackCheckList();
}


</script>

<template>
    <div class="account-search" v-if="mainStore.checkPermission(2001)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'inside'
                }">
                    约战内部
                </el-breadcrumb-item>
                <el-breadcrumb-item>IP检测功能</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <!-- <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-button type="primary" size="large" @click="addHomePageSav()">添加推荐录像
                                </el-button>
                                <el-button type="primary" size="large" @click="getHomePageRecSav()">刷新
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row> -->
                    <el-form ref="filterFormRef" :model="filterForm" status-icon :rules="filterFormRules"
                        class="filter-form">
                        <el-form-item label="排序类型" size="large">
                            <el-radio-group v-model="filterForm.sortType" size="large" @change="changeSortType">
                                <el-radio :label="1" size="large">按检测次数排序</el-radio>
                                <el-radio :label="2" size="large">按最后检测时间排序</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="显示封禁" size="large">
                            <el-space wrap>
                                <el-switch v-model="filterForm.showForbid" :active-value="1" :inactive-value="0"
                                    size="large" @change="changeShowForbid" />
                                <el-button type="primary" size="large" @click="getHackCheckList()">刷新
                                </el-button>
                            </el-space>

                        </el-form-item>
                    </el-form>
                    <el-table :data="hackCheck.data" border style="width: 100%">
                        <el-table-column prop="id" label="序号" />
                        <el-table-column label="玩家昵称">
                            <template #default="scope">
                                <el-link type="primary" :underline="false"
                                    @click="mainStore.gotoUserDetail(scope.row.user_id)">{{ scope.row.nickname }}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="user_id" label="玩家ID">
                        </el-table-column>
                        <el-table-column prop="check_time" label="检测时间">
                        </el-table-column>
                        <el-table-column prop="check_num" label="检测到次数">
                        </el-table-column>
                        <el-table-column prop="account_state" label="当前状态">
                            <template #default="scope">
                                <span style="color: #E6A23C;"
                                    v-if="scope.row.forbid_flag === 1 && scope.row.ban_flag === 0">{{
                                            scope.row.account_state
                                    }}</span>
                                <span style="color: #F56C6C;" v-else-if="scope.row.ban_flag === 1">{{
                                        scope.row.account_state
                                }}</span>
                                <span v-else>{{ scope.row.account_state }}</span>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination background layout="prev, pager, next" :total="hackCheck.total"
                        :current-page="currentPage" :page-size="15" @current-change="currentPageChange" />
                </el-space>


            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped>

</style>