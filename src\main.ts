import "element-plus/es/components/message/style/css";
import "element-plus/es/components/loading/style/css";
import "element-plus/es/components/message-box/style/css";
import "./assets/lib/normalize.css";
import { createApp } from "vue";
import { router } from "./router";
import { createPinia } from "pinia";
import App from "./App.vue";
import ElementPlus from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";

createApp(App)
  .use(router)
  .use(createPinia())
  .use(ElementPlus, {
    locale: zhCn,
  })
  .mount("#app");
