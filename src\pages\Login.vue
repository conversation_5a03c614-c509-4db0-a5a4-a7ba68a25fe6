<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus'
import { User, Lock, Phone } from '@element-plus/icons-vue';
import requset from '../api';
import crypto from 'crypto-js';
import { useMainStore } from '../store';
import { router } from '../router';

const mainStore = useMainStore();

let smsCodeTimer: number;
const getSmsCodeBtnLock = ref(true);
const getSmsCodeTimeLimit = ref(60);
const loginForm = reactive({
    account: "",
    password: "",
    smsNo: '',
    smsCode: '',
    secretCode: 0,
    countryCode: '0086'
});

const ruleFormRef = ref<FormInstance>();

const rules = reactive<FormRules>({
    account: [
        {
            required: true, message: '请输入邮箱', trigger: 'blur'
        }
    ],
    password: [
        {
            required: true, message: '请输入密码', trigger: 'blur'
        }
    ],
    smsCode: [
        {
            required: true, message: '请输入验证码', trigger: 'blur'
        }
    ],
});



async function login(formEl: FormInstance | undefined) {
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            const cry_pass = crypto.MD5(loginForm.password).toString();
            const smsCode = parseInt(loginForm.smsCode);
            const token = crypto.SHA1(`${loginForm.account}${cry_pass}${requset.ADMIN_TOKEN_KEY}`).toString();
            
            // if (!smsCode) {
            //     return mainStore.globalMessageTip('请输入正确的验证码', 3);
            // }

            mainStore.globalLoading(true);
            await requset.login({
                // email_no: '111',
                // email_code: '222',
                account: loginForm.account,
                password: cry_pass,
                token: token
            }).then(res => {
                if (res.code === 0) {
                    if (res.data && res.data.userInfo) {
                        mainStore.$patch({
                            identityToken: res.data.identityToken,
                            userInfo: {
                                user_id: res.data.userInfo.userID,
                                name: res.data.userInfo.name,
                                role: res.data.userInfo.role
                            }
                        });
                        const loginData = JSON.stringify({
                            user_id: res.data.userInfo.userID,
                            login_token: res.data.identityToken,
                            name: res.data.userInfo.name,
                            role: res.data.userInfo.role
                        });
                        localStorage.setItem('userInfo', loginData);
                    }
                    mainStore.globalMessageTip('登陆成功', 0);
                    router.push('/home');
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                            case 6:
                                mainStore.globalMessageTip('账号或密码错误', 3);
                                break;
                            case 7:
                                mainStore.globalMessageTip('登录失败次数过多，暂时禁止登陆', 3);
                                break;
                            case 8:
                            case 9:
                            case 10:
                                mainStore.globalMessageTip('验证码错误', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                mainStore.globalLoading(false);
            });
        } else {
            return;
        }
    });


}

async function getLoginSmsSecretCode() {
    if (!getSmsCodeBtnLock.value) {
        return;
    }
    if (!loginForm.account) {
        return mainStore.globalMessageTip('请输入账号', 3);
    }
    const token = crypto.SHA1(`${loginForm.countryCode}${loginForm.account}${requset.ADMIN_TOKEN_KEY}`).toString();

    let flag1 = false;
    getSmsCodeBtnLock.value = false;
    smsCodeTimer = setInterval(() => {
        if (getSmsCodeTimeLimit.value - 1 === 0) {
            clearInterval(smsCodeTimer);
            getSmsCodeBtnLock.value = true;
            getSmsCodeTimeLimit.value = 60;
        } else {
            getSmsCodeTimeLimit.value = getSmsCodeTimeLimit.value - 1;
        }

    }, 1000);
    await requset.getLoginSmsSecretCode({
        countryCode: loginForm.countryCode,
        account: loginForm.account,
        token: token
    }).then(res => {
        if (res.code === 0) {
            if (res.data) {
                if (res.data.smsNo && res.data.secretCode) {
                    loginForm.smsNo = res.data.smsNo;
                    loginForm.secretCode = ((res.data.secretCode ^ requset.ADMIN_SECRET_KEY1) - 1256) / 11;
                    flag1 = true;
                } else {
                    mainStore.globalMessageTip('获取验证码失败，请重新获取', 3);
                }
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                switch (res.code) {
                    case 5:
                        mainStore.globalMessageTip('账号不存在', 3);
                        break;
                    default:
                        break;
                }
            }
        }
    }).catch(err => {
        console.log(err);
    });
    if (flag1) {
        const secretCode = (((loginForm.secretCode + 378) * 22) ^ requset.ADMIN_SECRET_KEY2);
        const token2 = crypto.SHA1(`${loginForm.smsNo}${secretCode}${requset.ADMIN_TOKEN_KEY}`).toString();
        await requset.sendVerifySmsCode({
            smsNo: loginForm.smsNo,
            secretCode: secretCode,
            token: token2
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    if (res.data.smsNo) {
                        loginForm.smsNo = res.data.smsNo;
                    } else {
                        mainStore.globalMessageTip('获取验证码失败，请重新获取', 3);
                    }
                }
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                        case 6:
                            mainStore.globalMessageTip('验证码错误', 3);
                            break;
                        case 7:
                            mainStore.globalMessageTip('验证码发送失败', 3);
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch(err => {
            console.log(err);
        });
    }
}

</script>

<template>
    <div id="login">
        <el-card class="login-card">
            <!-- <div class="logo-wrap">
                <img src="../assets/img/logo.png" alt="">
            </div> -->
            <template #header>
                <div class="card-header">
                    <p>Global Match - Manage</p>
                </div>
            </template>
            <el-form ref="ruleFormRef" :model="loginForm" :rules="rules" status-icon class="login-form">
                <el-form-item prop="account">
                    <el-input placeholder="Email" v-model="loginForm.account" type="email" size="large" maxlength="255"
                        :prefix-icon="User" />
                </el-form-item>
                <el-form-item prop="password">
                    <el-input placeholder="Password" v-model="loginForm.password" type="password" size="large" maxlength="255"
                        :prefix-icon="Lock" />
                </el-form-item>
                <!-- <el-form-item prop="smsCode">
                    <div style="width: 100%;display: flex;justify-content: space-between;">
                        <el-input placeholder="Verification Code" v-model="loginForm.smsCode" type="text" size="large" maxlength="10"
                            :prefix-icon="Phone" style="width: 70%;" />
                        <el-button type="success" size="large" :disabled="!getSmsCodeBtnLock" style="width: 25%;"
                            @click="getLoginSmsSecretCode()">
                            <span v-if="getSmsCodeBtnLock">Send Email</span>
                            <span v-else>{{ getSmsCodeTimeLimit }}</span>
                        </el-button>
                    </div>
                </el-form-item> -->
            </el-form>
            <div class="login-btn">
                <el-button type="primary" size="large" @click="login(ruleFormRef)">Login</el-button>
            </div>
        </el-card>
    </div>
</template>

<style lang="less" scoped>
#login {
    position: relative;
    width: 100%;
    height: 100%;
    background: #606266;
}

.login-card {
    overflow: visible;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 500px;
    transform: translate(-50%, -50%);

    .card-header {
        position: relative;
        text-align: center;
    }
}

.el-form-item {

    // height: 60px;
    .el-input {
        // height: 40px;
    }
}

.login-btn {
    text-align: center;
}

.logo-wrap {
    position: absolute;
    top: -50%;
    left: 50%;
    width: 80px;
    height: 80px;
    box-shadow: 0 0 10px #ddd;
    border-radius: 50%;
    transform: translateX(-50%);

    img {
        width: 100%;
        height: 100%;
        vertical-align: top;
        border-radius: 50%;
        background-color: #555;
    }
}

@media screen and (max-width: 1120px) {
    .login-card {
        width: 90%;
    }
}
</style>
