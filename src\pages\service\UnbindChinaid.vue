<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { router } from '../../router';
import { AbroadUserState, UserChinaIdInfo } from '../../interface';
import { ElMessageBox } from 'element-plus';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);

const filterForm = reactive({
    nickname: '',
    account: '',
    user_id: '',
    find_type: 1
});

const userInfo = reactive({
    data: [] as Array<UserChinaIdInfo>
});

async function searchUsers() {
    if (!filterForm.account && !filterForm.nickname && !filterForm.user_id) {
        return mainStore.globalMessageTip('请输入查询信息!', 3);
    }
    if (filterForm.user_id !== '' && (parseInt(filterForm.user_id) < 0 || !parseInt(filterForm.user_id))) {
        return mainStore.globalMessageTip('请输入正确的用户ID!', 3);
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;

        await requset.getUserChinaIdInfo({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_nickname: filterForm.nickname,
            target_account: filterForm.account,
            target_userID: parseInt(filterForm.user_id),
            find_type: filterForm.find_type,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.userInfo) {
                    userInfo.data = res.data.userInfo;
                }
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('用户不存在', 3);
                            break;

                        default:
                            break;
                    }
                }
                userInfo.data = [];
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}



async function unbindUserChinaId(uid: number) {
    if (!uid) {
        return;
    }
    ElMessageBox.confirm(`确定要解绑该用户的身份证吗?`, '解绑身份证', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.unbindUserChinaId({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid,
                token: token
            }).then(res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('用户不存在', 3);
                                break;
                            case 6:
                                mainStore.globalMessageTip('用户已解绑，无需处理', 3);
                                break;
                            default:
                                break;
                        }
                    }

                }
            }).catch(err => {
                console.log(err);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

</script>

<template>
    <div class="account-search" v-if="mainStore.checkPermission(3001)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'service'
                }">
                    客服操作
                </el-breadcrumb-item>
                <el-breadcrumb-item>身份证解绑</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 50%" fill>
                    <el-form ref="ruleFormRef" :model="filterForm" status-icon class="login-form">
                        <el-form-item label="玩家昵称" label-width="120px">
                            <el-input placeholder="请输入玩家昵称" v-model="filterForm.nickname" size="large"
                                maxlength="255" />
                        </el-form-item>
                        <el-form-item label="玩家ID" label-width="120px">
                            <el-input placeholder="请输入玩家ID" v-model="filterForm.user_id" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item label="玩家账号" label-width="120px">
                            <el-input placeholder="请输入玩家账号" v-model="filterForm.account" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item label-width="120px">
                            <el-button type="primary" size="large" @click="searchUsers()">确定查找</el-button>
                        </el-form-item>
                    </el-form>
                </el-space>
                <el-table :data="userInfo.data" border style="width: 100%">
                    <el-table-column prop="user_id" label="ID" />
                    <el-table-column prop="account" label="账号" />
                    <el-table-column prop="nickname" label="昵称">
                        <template #default="scope">
                            <el-link type="primary" :underline="false"
                                @click="mainStore.gotoUserDetail(scope.row.user_id)">{{ scope.row.nickname }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="china_id" label="身份证号">
                    </el-table-column>
                    <el-table-column prop="china_name" label="姓名">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="danger" @click="unbindUserChinaId(scope.row.user_id)">解绑
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped>

</style>