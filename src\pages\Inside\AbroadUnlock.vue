<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { router } from '../../router';
import { AbroadUserState } from '../../interface';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);

const filterForm = reactive({
    nickname: '',
    account: '',
    user_id: '',
    find_type: 1
});

const abroadUser = reactive({
    data: [] as Array<AbroadUserState>
});

async function searchUsers() {
    if (!filterForm.account && !filterForm.nickname && !filterForm.user_id) {
        return mainStore.globalMessageTip('请输入查询信息!', 3);
    }
    if (filterForm.user_id !== '' && (parseInt(filterForm.user_id) < 0 || !parseInt(filterForm.user_id))) {
        return mainStore.globalMessageTip('请输入正确的用户ID!', 3);
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;

        await requset.getAbroadUserState({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_nickname: filterForm.nickname,
            target_account: filterForm.account,
            target_userID: parseInt(filterForm.user_id),
            find_type: filterForm.find_type,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.abroadUsers) {
                    abroadUser.data = res.data.abroadUsers;
                }
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                        case 6:
                            mainStore.globalMessageTip('用户不存在', 3);
                            break;

                        default:
                            break;
                    }
                }
                abroadUser.data = [];
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}



async function abroadUserUnlock(uid: number) {
    if (!uid) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${uid}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.abroadUserUnlock({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetUserID: uid,
            token: token
        }).then(res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                        case 6:
                            mainStore.globalMessageTip('用户不存在', 3);
                            break;
                        case 7:
                            mainStore.globalMessageTip('帐号状态异常，请联系周玉华', 3);
                            break;
                        case 8:
                            mainStore.globalMessageTip('无需处理', 3);
                            break;
                        default:
                            break;
                    }
                }

            }
        }).catch(err => {
            console.log(err);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

</script>

<template>
    <div class="account-search" v-if="mainStore.checkPermission(2002)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'inside'
                }">
                    约战内部
                </el-breadcrumb-item>
                <el-breadcrumb-item>海外用户解锁</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 50%" fill>
                    <el-form ref="ruleFormRef" :model="filterForm" status-icon class="login-form">
                        <el-form-item prop="account" label="玩家昵称" label-width="120px">
                            <el-input placeholder="请输入玩家昵称" v-model="filterForm.nickname" size="large"
                                maxlength="255" />
                        </el-form-item>
                        <el-form-item prop="account" label="玩家ID" label-width="120px">
                            <el-input placeholder="请输入玩家ID" v-model="filterForm.user_id" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item prop="account" label="玩家账号" label-width="120px">
                            <el-input placeholder="请输入玩家账号" v-model="filterForm.account" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item prop="account" label-width="120px">
                            <el-button type="primary" size="large" @click="searchUsers()">确定查找</el-button>
                        </el-form-item>
                    </el-form>
                </el-space>
                <el-table :data="abroadUser.data" border style="width: 100%">
                    <el-table-column prop="user_id" label="ID" />
                    <el-table-column prop="account" label="账号" />
                    <el-table-column prop="nickname" label="昵称">
                    </el-table-column>
                    <el-table-column prop="move_state_1" label="1.0状态">
                    </el-table-column>
                    <el-table-column prop="move_state_2" label="2.0状态">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="danger" @click="abroadUserUnlock(scope.row.user_id)"
                                v-if="scope.row.move_state_1 == 3 &&  scope.row.move_state_2 == 3">解锁
                            </el-button>
                            <span
                                v-else-if="(scope.row.move_state_1 == 3 &&  scope.row.move_state_2 == 4) || (scope.row.move_state_1 == 4 &&  scope.row.move_state_2 == 3)">状态异常，请联系周玉华</span>
                            <span v-else>无需解锁</span>
                        </template>
                    </el-table-column>
                </el-table>

            </el-card>
        </el-space>
    </div>
</template>

<style lang="less" scoped>

</style>