<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import { NicknameReview } from '../../interface';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);

getAvatarList();

async function getAvatarList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getAvatarList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.avatarList) {
                    outsourcingStore.avatarReviewList = res.data.avatarList;
                    outsourcingStore.avatarReviewNum = res.data.total;
                    outsourcingStore.avatarReviewProcessed = res.data.processed;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        }).finally(() => {
            loading.value = false;
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function correctAvatar(targetID: number, flag: number) {
    if (targetID) {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${targetID}${flag}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            loading.value = true;
            await requset.correctUserAvatar({
                userID: userID,
                targetAvatarID: targetID,
                identityToken: identityToken,
                authorityID: authorityID,
                handleType: flag,
                token: token
            }).then(res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    outsourcingStore.removeTheAvatarReview(targetID);
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该用户已被其他管理员处理', 3);
                                outsourcingStore.removeTheAvatarReview(targetID);
                                break;

                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                loading.value = false;
            });
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }
}

async function processAllAvatar() {
    if (outsourcingStore.avatarReviewList.length > 0) {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const targetAvatarID = outsourcingStore.avatarReviewList.map(item => item.id).join(',');
        const token = crypto.SHA1(`${userID}${authorityID}${targetAvatarID}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            loading.value = true;
            await requset.processAllUserAvatar({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetAvatarID: targetAvatarID,
                token: token
            }).then(res => {
                if (res.code === 0) {
                    getAvatarList();
                    mainStore.globalMessageTip('处理成功', 0);
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                loading.value = false;
            });
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    } else {
        mainStore.globalMessageTip('请刷新列表', 2);
    }
}

</script>

<template>
    <div class="nickname-review" v-if="mainStore.checkPermission(1103)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'outsourcing'
                }">
                    外协管理
                </el-breadcrumb-item>
                <el-breadcrumb-item>头像审核</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>待处理任务条数：{{ outsourcingStore.avatarReviewNum }}</span> <span>今日已处理：{{
                                    outsourcingStore.avatarReviewProcessed
                                }}</span>
                                <el-button type="primary" @click="getAvatarList()" size="large">刷新列表
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-space wrap>
                            <el-card v-for="(item, index) in outsourcingStore.avatarReviewList" :key="item.id">
                                <el-space direction="vertical">
                                    <!-- <img :src="item.icon_url" class="image" /> -->
                                    <el-avatar shape="square" :size="200" :src="item.icon_url" />
                                    <span>用户ID：<el-link type="primary" :underline="false"
                                            @click="mainStore.gotoUserDetail(item.user_id)">{{ item.user_id
                                            }}</el-link></span>
                                    <el-col>
                                        <el-button type="danger" @click="correctAvatar(item.id, 0)" size="large">头像违规
                                        </el-button>
                                    </el-col>
                                    <el-col>
                                        <el-button type="danger" @click="correctAvatar(item.id, 1)" size="large">违规(补头像卡)
                                        </el-button>
                                    </el-col>
                                </el-space>

                            </el-card>
                        </el-space>
                    </el-row>
                    <div style="text-align: center;">
                        <el-button type="success" size="large" @click="processAllAvatar()">以上无问题
                        </el-button>
                    </div>
                </el-space>


            </el-card>
        </el-space>


    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped></style>