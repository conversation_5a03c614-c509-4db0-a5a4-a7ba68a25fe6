<script setup lang="ts">
import { ArrowRight, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { computed, reactive, ref } from 'vue';
import { FormInstance, FormRules, genFileId, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox } from 'element-plus';
import Big from 'big.js';
import { SettlementRecord } from '../../interface';

const mainStore = useMainStore();

await mainStore.getGameCompanyList();

const addGameDialog = ref(false);
const editGameDialog = ref(false);
const loading = ref(false);

const addUploadRef = ref<UploadInstance>();
const editUploadRef = ref<UploadInstance>();

const filterForm = reactive({
    company_id: mainStore.gameCompanyList[0]?.company_id || '',
});


const settmentRecord = ref([] as Array<SettlementRecord>);


getSettlementRecord();

function selectCompany(val: number) {
    mainStore.getGameList();
}


async function getSettlementRecord() {
    if (filterForm.company_id) {
        loading.value = true;
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${filterForm.company_id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        if (userID && identityToken) {
            await requset
                .getGameCompanySettlement({
                    userID: userID,
                    identityToken: identityToken,
                    authorityID: authorityID,
                    companyID: Number(filterForm.company_id),
                    token: token,
                })
                .then((res) => {
                    if (res.code === 0) {
                        if (res.data && res.data.settlementRecord) {
                            settmentRecord.value = res.data.settlementRecord;
                        }
                    } else {
                        mainStore.dealResponseErrInfo(res.code);
                    }
                })
                .catch((err) => {
                    console.log(err);
                })
                .finally(() => {
                    loading.value = false;
                });
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }
}


</script>

<template>
    <div class="banner-config">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item>
                    Manage
                </el-breadcrumb-item>
                <el-breadcrumb-item>Settlement History</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>Game company: </span>
                                <el-select v-model="filterForm.company_id" placeholder="请选择游戏公司" size="large"
                                    @change="selectCompany">
                                    <el-option v-for="(item, index) in mainStore.gameCompanyList" :key="item.company_id"
                                        :label="item.company_name" :value="item.company_id" />
                                </el-select>

                                <el-button type="primary" size="large" @click="getSettlementRecord">Refresh
                                </el-button>

                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table :data="settmentRecord" border style="width: 100%">
                        <el-table-column prop="id" label="ID" />
                        <el-table-column label="Settlement amount">
                            <template #default="scope">
                                ${{ scope.row.amount }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="time" label="Settlement Time" />
                    </el-table>
                </el-space>
            </el-card>
        </el-space>
    </div>
</template>

<style lang="less" scoped></style>