<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ArrowRight, House } from '@element-plus/icons-vue';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { onMounted, reactive, ref } from 'vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { router } from '../../router';
import { CoopGameInfo, Forbid, HackCheckRecord, IpInfo, IpQuery, OldNicknameItem, RecentLoginItem, TalkMsg, UserAvatar, UserTaskRecord, VsGameInfo } from '../../interface';
import axios from "axios";
import { ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import * as echarts from 'echarts/core';
import {
    TitleComponent,
    TitleComponentOption,
    ToolboxComponent,
    ToolboxComponentOption,
    TooltipComponent,
    TooltipComponentOption,
    GridComponent,
    GridComponentOption,
    LegendComponent,
    LegendComponentOption
} from 'echarts/components';
import {
    LineChart,
    LineSeriesOption,
    BarChart
} from 'echarts/charts';
import {
    UniversalTransition
} from 'echarts/features';
import {
    CanvasRenderer
} from 'echarts/renderers';
import { utils, writeFile } from 'xlsx';


echarts.use(
    [TitleComponent, ToolboxComponent, TooltipComponent, GridComponent, LegendComponent, LineChart, CanvasRenderer, UniversalTransition, BarChart]
);



const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const tabIndex = ref('day');
const countType = ref(1);

const dayRechargeFetchFlag = ref(false);
const weekRechargeFetchFlag = ref(false);
const monthRechargeFetchFlag = ref(false);

const dayFilterForm = reactive({
    server_id: -1,
    game_id: -1,
    date_range: ['', ''],
    quick_select: 1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
});
const tableFilterForm = reactive({
    date_range: ['', ''],
});
const dayRechargeData = reactive({
    recharge_user: 0,
    recharge_num: 0,
    recharge_new: 0,
    cny_total: 0,
    usd_total: 0
});

const stgameTabelData = reactive({
    data: [] as Array<any>
});


const weekFilterForm = reactive({
    platform: -1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
    settlement_type: mainStore.channelCountType[0] ? mainStore.channelCountType[0].value : '',
});
const weekRechargeData = reactive({
    recharge_user: 0,
    recharge_num: 0,
    recharge_new: 0,
    cny_total: 0,
    usd_total: 0
});

const monthFilterForm = reactive({
    platform: -1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
    settlement_type: mainStore.channelCountType[0] ? mainStore.channelCountType[0].value : '',
});
const monthRechargeData = reactive({
    recharge_user: 0,
    recharge_num: 0,
    recharge_new: 0,
    cny_total: 0,
    usd_total: 0
});


mainStore.$subscribe((mutation, state) => {
    if (state.channelList.length > 0) {
        dayFilterForm.app_channel = mainStore.channelList[0].channel_id;
        weekFilterForm.app_channel = mainStore.channelList[0].channel_id;
        monthFilterForm.app_channel = mainStore.channelList[0].channel_id;
    }
});

dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
dayFilterForm.date_range[1] = mainStore.quckGetDate(1);

function dayTimeRadioChange(val: any) {
    switch (val) {
        case 1:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(1);
            break;
        case 2:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(2);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 3:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(3);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 4:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(4);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        default:
            break;
    }
}

function weekSettlementTypeChange(val: any) {
    weekFilterForm.settlement_type = val;
}

function monthSettlementTypeChange(val: any) {
    monthFilterForm.settlement_type = val;
}


async function statisticsGameSalesData(count_type?: number) {

    let game_id = -1;
    let source_type = -1;
    let start_time = '';
    let end_time = '';

    // switch (countType.value) {
    //     case 1:
    //         source_type = parseInt(String(dayFilterForm.source_type));
    //         game_id = dayFilterForm.game_id;
    //         if (dayFilterForm.date_range.length > 0) {
    //             start_time = dayFilterForm.date_range[0];
    //             end_time = dayFilterForm.date_range[1];
    //         }
    //         break;
    //     case 2:
    //         source_type = parseInt(String(weekFilterForm.source_type));
    //         game_code = weekFilterForm.game_code;
    //         break;
    //     case 3:
    //         source_type = parseInt(String(monthFilterForm.source_type));
    //         game_code = monthFilterForm.game_code;
    //         break;
    //     default:
    //         break;
    // }

    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${countType.value}${source_type}${game_id}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.statisticsGameSalesData({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            gameID: game_id,
            countType: countType.value,
            sourceType: source_type,
            startTime: start_time,
            endTime: end_time,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                if (res.data) {
                    // switch (count_type) {
                    //     case 1:
                    //         dayActiveFetchFlag.value = true;
                    //         setTimeout(() => {
                    //             renderDayChart(res.data.xData, res.data.openData, res.data.userData);
                    //         }, 0);
                    //         break;
                    //     case 2:
                    //         weekActiveFetchFlag.value = true;
                    //         setTimeout(() => {
                    //             renderWeekChart(res.data.xData, res.data.openData, res.data.userData);
                    //         }, 0);
                    //         break;
                    //     case 3:
                    //         monthActiveFetchFlag.value = true;
                    //         setTimeout(() => {
                    //             renderMonthChart(res.data.xData, res.data.openData, res.data.userData);
                    //         }, 0);
                    //         break;
                    //     default:
                    //         break;
                    // }
                    dayRechargeFetchFlag.value = true;
                    setTimeout(() => {
                        renderDayChart(res.data.xData, res.data.saleData);
                    }, 0);
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function statisticsGameSalesDataTable() {
    let start_time = '';
    let end_time = '';

    if (tableFilterForm.date_range.length > 0) {
        start_time = tableFilterForm.date_range[0];
        end_time = tableFilterForm.date_range[1];
    } else {
        return;
    }

    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();

    if (userID && identityToken) {
        loading.value = true;
        await requset.statisticsGameSalesTableData({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            startTime: start_time,
            endTime: end_time,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                if (res.data) {
                    stgameTabelData.data = res.data.stgameTableData;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function renderDayChart(xData: Array<any>, data1: Array<number>) {
    const dayActiveCanvas = document.getElementById('dayRechargeDataCanvas');
    if (dayActiveCanvas) {
        const dayActiveChart = echarts.init(dayActiveCanvas);
        const option = {
            title: {
                text: 'Game sales data'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {},
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: [
                {
                    type: 'category',
                    data: xData,
                    axisLabel: {
                        interval: 0
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: [
                {
                    name: 'Sales',
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: data1
                },
            ]
        };
        option && dayActiveChart.setOption(option);
    }
}

function exportExcel() {
    const header = ["日期", "游戏名称", "点击量", "用户数"];
    const rows: any[][] = [header];
    const fileName = `${tableFilterForm.date_range[0]}—${tableFilterForm.date_range[1]}.xlsx`;

    stgameTabelData.data.forEach(item => {
        const row = [
            item.date,
            item.game_name,
            item.open_times,
            item.user_num
        ];
        rows.push(row);
    });

    const excel_name = '数据统计';

    const excel_file = utils.book_new();
    const excel_sheet = utils.aoa_to_sheet(rows);

    utils.book_append_sheet(excel_file, excel_sheet, excel_name);
    writeFile(excel_file, fileName);
}

</script>

<template>
    <div class="user-detai" v-if="mainStore.checkPermission(4201)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
        name: 'statistics'
    }">
                    Statistics
                </el-breadcrumb-item>
                <el-breadcrumb-item>Game Sales</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill :size="32">
                    <el-tabs type="border-card" v-model="tabIndex">
                        <el-tab-pane label="Daily Sales" name="day">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="dayFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="Time" size="large">
                                                <el-col :span="7">
                                                    <el-date-picker v-model="dayFilterForm.date_range" type="daterange"
                                                        start-placeholder="Start Date" end-placeholder="End Date"
                                                        size="large" value-format="YYYY-MM-DD"
                                                        :default-value="[new Date(), new Date()]" />
                                                </el-col>
                                                <el-col :span="10">
                                                    <el-radio-group v-model="dayFilterForm.quick_select" size="large"
                                                        @change="dayTimeRadioChange">
                                                        <el-radio :label="1" size="large">Today</el-radio>
                                                        <el-radio :label="2" size="large">Yesterday</el-radio>
                                                        <el-radio :label="3" size="large">Last 7 days</el-radio>
                                                        <el-radio :label="4" size="large">Last 30 days</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-form-item>
                                            <!-- <el-form-item label="Region" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="dayFilterForm.app_channel"
                                                        placeholder="Select Region" size="large">
                                                        <el-option v-for="item in mainStore.serverList"
                                                            :key="item.server_id" :label="item.server_name"
                                                            :value="item.server_id" />
                                                    </el-select>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="channel" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="dayFilterForm.app_channel"
                                                        placeholder="Select Channel" size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>
                                            </el-form-item> -->
                                            <el-form-item label="game" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="dayFilterForm.game_id" placeholder="Select Game"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.shopGameList"
                                                            :key="item.game_id" :label="item.game_name"
                                                            :value="item.game_id" />
                                                    </el-select>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click="statisticsGameSalesData(1)"
                                                        size="large">Inquire
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>

                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="dayRechargeDataCanvas" style="height: 400px;" v-if="dayRechargeFetchFlag">
                                </div>
                                <!-- <div id="dayIncomeDataCanvas" style="height: 400px;" v-if="dayRechargeFetchFlag"></div>
                                <el-row v-if="dayRechargeFetchFlag">
                                    总计：充值人数&nbsp;<span>{{ dayRechargeData.recharge_user
                                        }}</span>&nbsp;人，充值次数&nbsp;<span>{{ dayRechargeData.recharge_num
                                        }}</span>&nbsp;次，首充人数&nbsp;<span>{{ dayRechargeData.recharge_new
                                        }}</span>&nbsp;人，总收入&nbsp;<span>{{ dayRechargeData.cny_total }}</span>&nbsp;元。
                                </el-row> -->
                            </el-space>

                        </el-tab-pane>
                        <el-tab-pane label="Weekly Sales" name="week">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="weekFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="weekFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="结算" size="large">
                                                <el-col :span="6">
                                                    <el-radio-group v-model="weekFilterForm.settlement_type"
                                                        size="large" @change="weekSettlementTypeChange">
                                                        <el-radio :label="item.value" size="large"
                                                            v-for="(item, index) in mainStore.channelCountType"
                                                            :key="item.value">{{ item.label }}</el-radio>
                                                    </el-radio-group>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click=""
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>

                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="weekRechargeDataCanvas" style="height: 400px;" v-if="weekRechargeFetchFlag">
                                </div>
                                <div id="weekIncomeDataCanvas" style="height: 400px;" v-if="weekRechargeFetchFlag">
                                </div>
                                <el-row v-if="weekRechargeFetchFlag">
                                    总计：充值人数&nbsp;<span>{{ weekRechargeData.recharge_user
                                        }}</span>&nbsp;人，充值次数&nbsp;<span>{{ weekRechargeData.recharge_num
                                        }}</span>&nbsp;次，首充人数&nbsp;<span>{{ weekRechargeData.recharge_new
                                        }}</span>&nbsp;人，总收入&nbsp;<span>{{ weekRechargeData.cny_total }}</span>&nbsp;元。
                                </el-row>
                            </el-space>

                        </el-tab-pane>
                        <el-tab-pane label="Monthly Sales" name="month">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="monthFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="monthFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="结算" size="large">
                                                <el-col :span="6">
                                                    <el-radio-group v-model="monthFilterForm.settlement_type"
                                                        size="large" @change="monthSettlementTypeChange">
                                                        <el-radio :label="item.value" size="large"
                                                            v-for="(item, index) in mainStore.channelCountType"
                                                            :key="item.value">{{ item.label }}</el-radio>
                                                    </el-radio-group>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click=""
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>

                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="monthRechargeDataCanvas" style="height: 400px;" v-if="monthRechargeFetchFlag">
                                </div>
                                <div id="monthIncomeDataCanvas" style="height: 400px;" v-if="monthRechargeFetchFlag">
                                </div>
                                <el-row v-if="monthRechargeFetchFlag">
                                    总计：充值人数&nbsp;<span>{{ monthRechargeData.recharge_user
                                        }}</span>&nbsp;人，充值次数&nbsp;<span>{{ monthRechargeData.recharge_num
                                        }}</span>&nbsp;次，首充人数&nbsp;<span>{{ monthRechargeData.recharge_new
                                        }}</span>&nbsp;人，总收入&nbsp;<span>{{ monthRechargeData.cny_total }}</span>&nbsp;元。
                                </el-row>
                            </el-space>

                        </el-tab-pane>
                        <el-tab-pane label="Table Statistics" name="table">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="dayFilterForm">
                                            <el-form-item label="Time" size="large">
                                                <el-col :span="7">
                                                    <el-date-picker v-model="tableFilterForm.date_range" type="daterange"
                                                        start-placeholder="Start Date" end-placeholder="End Date" size="large"
                                                        value-format="YYYY-MM-DD"
                                                        :default-value="[new Date(), new Date()]" />
                                                </el-col>
                                                <el-col :span="3">
                                                    <el-button type="primary" @click="statisticsGameSalesDataTable()"
                                                        size="large">Inquire
                                                    </el-button>
                                                </el-col>
                                                <el-link type="primary" @click="exportExcel()" v-if="stgameTabelData.data.length > 0">Export Excel table</el-link>
                                            </el-form-item>
                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <el-table v-loading="loading" :data="stgameTabelData.data" border style="width: 100%">
                                    <el-table-column prop="date" label="Date" />
                                    <el-table-column prop="game_name" label="Game" />
                                    <el-table-column prop="sale_num" label="sales" />
                                </el-table>
                            </el-space>
                        </el-tab-pane>
                    </el-tabs>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped></style>