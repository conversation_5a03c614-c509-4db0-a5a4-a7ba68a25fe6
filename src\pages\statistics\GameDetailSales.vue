<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ArrowRight, House } from '@element-plus/icons-vue';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { onMounted, reactive, ref } from 'vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { router } from '../../router';
import { CoopGameInfo, Forbid, HackCheckRecord, IpInfo, IpQuery, OldNicknameItem, RecentLoginItem, TalkMsg, UserAvatar, UserTaskRecord, VsGameInfo } from '../../interface';
import axios from "axios";
import { ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import * as echarts from 'echarts/core';
import {
    TitleComponent,
    TitleComponentOption,
    ToolboxComponent,
    ToolboxComponentOption,
    TooltipComponent,
    TooltipComponentOption,
    GridComponent,
    GridComponentOption,
    LegendComponent,
    LegendComponentOption
} from 'echarts/components';
import {
    LineChart,
    LineSeriesOption,
    BarChart
} from 'echarts/charts';
import {
    UniversalTransition
} from 'echarts/features';
import {
    CanvasRenderer
} from 'echarts/renderers';
import { utils, writeFile } from 'xlsx';
import Big, { BigSource } from 'big.js';
import { number } from 'echarts';


echarts.use(
    [TitleComponent, ToolboxComponent, TooltipComponent, GridComponent, LegendComponent, LineChart, CanvasRenderer, UniversalTransition, BarChart]
);



const route = useRoute();
const mainStore = useMainStore();

const loading = ref(false);

const gid = ref(0);
if (route.query && route.query.gid) {
    gid.value = parseInt(route.query.gid.toString());
    if (!gid.value || gid.value <= 0) {
        router.push({
            name: 'home'
        });
        mainStore.globalMessageTip('GameId Error!', 3);
    }
}

const dayRechargeFetchFlag = ref(false);

const dayFilterForm = reactive({
    server_id: -1,
    company_id: '' as number | string,
    game_id: '' as number | string,
    date_range: ['', ''],
    quick_select: 1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
});
const tableFilterForm = reactive({
    date_range: ['', ''],
});
const dayRechargeData = reactive({
    sale_total: 0,
    gold_total: 0,
    usd_total: 0
});

const stgameTabelData = reactive({
    data: [] as Array<any>
});


await fetchCompanyMallGame();

dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
dayFilterForm.date_range[1] = mainStore.quckGetDate(1);


async function fetchCompanyMallGame() {
    mainStore.globalLoading(true);
    await mainStore.getGameCompanyList();
    if (mainStore.gameCompanyList[0]) {
        dayFilterForm.company_id = mainStore.gameCompanyList[0].company_id;
    }
    if (dayFilterForm.company_id) {
        await mainStore.getMallGameWithCompany(Number(dayFilterForm.company_id));
    }
    if (mainStore.getterCurrentCompany(Number(dayFilterForm.company_id)) && mainStore.getterCurrentCompany(Number(dayFilterForm.company_id)).mall_games) {
        if (gid.value) {
            dayFilterForm.game_id = gid.value;
        } else {
            dayFilterForm.game_id = mainStore.getterCurrentCompany(Number(dayFilterForm.company_id)).mall_games[0].game_id;
        }
    }
    mainStore.globalLoading(false);
}

function dayTimeRadioChange(val: any) {
    switch (val) {
        case 1:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(1);
            break;
        case 2:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(2);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 3:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(3);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 4:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(4);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        default:
            break;
    }
}


async function statisticsGameSalesData(count_type?: number) {

    let start_time = '';
    let end_time = '';

    switch (count_type) {
        case 1:
            if (dayFilterForm.date_range.length > 0) {
                start_time = dayFilterForm.date_range[0];
                end_time = dayFilterForm.date_range[1];
            }
            break;
        case 2:
            break;
        case 3:
            break;
        default:
            break;
    }

    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${count_type}${dayFilterForm.game_id}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.statisticsGameSalesDetailData({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            companyID: Number(dayFilterForm.company_id),
            gameID: dayFilterForm.game_id,
            countType: count_type,
            startTime: start_time,
            endTime: end_time,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                if (res.data) {
                    switch (count_type) {
                        case 1:
                            res.data.goldTotal = new Big(res.data.goldTotal).div(100).toNumber();
                            if (res.data.goldData) {
                                res.data.goldData = res.data.goldData.map((item: number | BigSource) => { return new Big(item).div(100).toNumber() });
                            }
                            dayRechargeFetchFlag.value = true;


                            setTimeout(() => {
                                renderDayChart(res.data.xData, res.data.saleNumData, res.data.goldData);
                                dayRechargeData.sale_total = res.data.saleNumTotal;
                                dayRechargeData.gold_total = res.data.goldTotal;
                                // dayRechargeData.usd_total = new Big(res.data.gold_total).div(100);
                            }, 0);

                            break;
                        default:
                            break;
                    }
                    mainStore.lastStatisticsTime = res.data.update_time;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function renderDayChart(xData: Array<any>, data1: Array<number>, data2: Array<number>) {
    const dayRechargeDataCanvas = document.getElementById('dayRechargeDataCanvas');


    const dayIncomeDataCanvas = document.getElementById('dayIncomeDataCanvas');
    if (dayRechargeDataCanvas) {
        const dayRechargeChart = echarts.init(dayRechargeDataCanvas);
        const option = {
            title: {
                text: 'Sales'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['Sales quantity', 'Sales Amount($)']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: 'Sales quantity',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data1,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
                {
                    name: 'Sales Amount($)',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data2,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
            ]
        };
        option && dayRechargeChart.setOption(option);
    }
    // if (dayIncomeDataCanvas) {
    //     const dayIncomeChart = echarts.init(dayIncomeDataCanvas);
    //     const option = {
    //         title: {
    //             text: '收入'
    //         },
    //         tooltip: {
    //             trigger: 'axis'
    //         },
    //         legend: {
    //             data: ['消费金额']
    //         },
    //         grid: {
    //             left: '3%',
    //             right: '4%',
    //             bottom: '3%',
    //             containLabel: true
    //         },
    //         toolbox: {
    //             feature: {
    //                 saveAsImage: {},
    //                 dataView: {
    //                     show: true
    //                 }
    //             }
    //         },
    //         xAxis: {
    //             type: 'category',
    //             boundaryGap: false,
    //             data: xData
    //         },
    //         yAxis: {
    //             type: 'value'
    //         },
    //         series: [
    //             {
    //                 name: '消费金额',
    //                 type: 'line',
    //                 smooth: true,
    //                 areaStyle: {},
    //                 data: data4,
    //                 label: {
    //                     show: true,
    //                     position: 'outside'
    //                 },
    //                 emphasis: {
    //                     focus: 'series'
    //                 },
    //             },
    //         ]
    //     };
    //     option && dayIncomeChart.setOption(option);
    // }
}


function exportExcel() {
    const header = ["日期", "游戏名称", "点击量", "用户数"];
    const rows: any[][] = [header];
    const fileName = `${tableFilterForm.date_range[0]}—${tableFilterForm.date_range[1]}.xlsx`;

    stgameTabelData.data.forEach(item => {
        const row = [
            item.date,
            item.game_name,
            item.open_times,
            item.user_num
        ];
        rows.push(row);
    });

    const excel_name = '数据统计';

    const excel_file = utils.book_new();
    const excel_sheet = utils.aoa_to_sheet(rows);

    utils.book_append_sheet(excel_file, excel_sheet, excel_name);
    writeFile(excel_file, fileName);
}

async function selectCompany(val: number) {
    if (val === dayFilterForm.company_id) {
        return;
    }
    loading.value = true;
    if (dayFilterForm.company_id) {
        await mainStore.getMallGameWithCompany(Number(dayFilterForm.company_id));
    }
    if (mainStore.getterCurrentCompany(Number(dayFilterForm.company_id)) && mainStore.getterCurrentCompany(Number(dayFilterForm.company_id)).mall_games) {
        dayFilterForm.game_id = mainStore.getterCurrentCompany(Number(dayFilterForm.company_id)).mall_games[0].game_id;
        await statisticsGameSalesData(1);
    }
    loading.value = false;
}

async function selectGame(val: number) {
    await statisticsGameSalesData(1);
}

onMounted(() => {
    statisticsGameSalesData(1);
});

</script>

<template>
    <div class="user-detai">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                name: 'statistics'
            }">
                    Statistics
                </el-breadcrumb-item>
                <el-breadcrumb-item>Game sales details</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill :size="32">
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>Time: </span>
                                <el-date-picker v-model="dayFilterForm.date_range" type="daterange"
                                    start-placeholder="Start Date" end-placeholder="End Date" size="large"
                                    value-format="YYYY-MM-DD" :default-value="[new Date(), new Date()]" />
                                <el-radio-group v-model="dayFilterForm.quick_select" size="large"
                                    @change="dayTimeRadioChange">
                                    <el-radio :label="1" size="large">Today</el-radio>
                                    <el-radio :label="2" size="large">Yesterday</el-radio>
                                    <el-radio :label="3" size="large">Last 7 days</el-radio>
                                    <el-radio :label="4" size="large">Last 30 days</el-radio>
                                </el-radio-group>
                            </el-space>

                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>Company: </span>
                                <el-select v-model="dayFilterForm.company_id" placeholder="Please select company"
                                    size="large" @change="selectCompany">
                                    <el-option v-for="(item, index) in mainStore.gameCompanyList" :key="item.company_id"
                                        :label="item.company_name" :value="item.company_id" />
                                </el-select>
                                <span>Game: </span>
                                <el-select v-model="dayFilterForm.game_id" placeholder="Please select a game"
                                    size="large" @change="selectGame">
                                    <el-option label="All" :value="-1" />
                                    <el-option
                                        v-for="(item, index) in mainStore.getterCurrentCompany(Number(dayFilterForm.company_id))?.mall_games"
                                        :key="item.game_id" :label="item.game_name" :value="item.game_id" />
                                </el-select>
                                <el-button type="primary" @click="statisticsGameSalesData(1)" size="large">Inquire
                                </el-button>
                                <b>Last update: {{ mainStore.lastStatisticsTime }} </b>
                                <span>(Hourly Statistics)</span>
                            </el-space>
                        </el-col>
                    </el-row>
                    <div id="dayRechargeDataCanvas" style="height: 400px;" v-if="dayRechargeFetchFlag">
                    </div>
                    <el-row v-if="dayRechargeFetchFlag">
                        Sales quantity: &nbsp;<span>{{ dayRechargeData.sale_total
                            }}</span>&nbsp;，Sales amount: &nbsp;<span>${{ dayRechargeData.gold_total
                            }}</span>&nbsp;。
                    </el-row>
                    <!-- <div id="dayIncomeDataCanvas" style="height: 400px;" v-if="dayRechargeFetchFlag"></div> -->

                </el-space>
            </el-card>
        </el-space>
    </div>
</template>

<style lang="less" scoped></style>