<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { router } from '../../router';
import { AbroadUserState, UserBuyGoodsRecord, UserChinaIdInfo, UserTradeInfo } from '../../interface';
import { ElMessageBox } from 'element-plus';


const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);

const tradeTableRef = ref();
const refundTicketDialog = ref(false);
const refundTicketFormRef = ref<FormInstance>();
const refundTicketForm = reactive({
    ticket_num: '',
    max_ticket: 0,
    trade_no: '',
    user_ticket: 0,
    target_user: 0
});
const refundTicketFormRules = reactive<FormRules>({
    ticket_num: [
        {
            required: true, message: '请输入点券数量', trigger: 'blur'
        }
    ],
});

const filterForm = reactive({
    tradeNo: '',
});

const tradeInfo = reactive({
    data: [] as Array<UserTradeInfo>,
    buyGoodsRecord: [] as Array<UserBuyGoodsRecord>
});

async function searchTradeNo() {
    if (!filterForm.tradeNo) {
        return mainStore.globalMessageTip('请输入订单号!', 3);
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${filterForm.tradeNo}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.tradeNoSearch({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            tradeNo: filterForm.tradeNo,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    if (res.data.tradeInfo) {
                        tradeInfo.data = res.data.tradeInfo;
                    }
                    if (res.data.buyGoodsRecord) {
                        tradeInfo.buyGoodsRecord = res.data.buyGoodsRecord;
                    }
                }
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('订单号不存在', 3);
                            break;

                        default:
                            break;
                    }
                }
                tradeInfo.data = [];
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function refundTicket(item: UserTradeInfo) {
    refundTicketForm.ticket_num = '';
    refundTicketForm.max_ticket = item.tickets;
    refundTicketForm.trade_no = item.trade_no;
    refundTicketForm.user_ticket = item.left_ticket;
    refundTicketForm.target_user = item.user_id;
    refundTicketDialog.value = true;
}

async function refundTicketSubmit(formEl: FormInstance | undefined) {
    if (!formEl) {
        return;
    }

    await formEl.validate(async (valid, fields) => {
        if (valid) {
            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const target_user = refundTicketForm.target_user;
            const trade_no = refundTicketForm.trade_no;
            const ticket_num = parseInt(refundTicketForm.ticket_num);
            const token = crypto.SHA1(`${userID}${authorityID}${target_user}${trade_no}${ticket_num}${requset.ADMIN_TOKEN_KEY}`).toString();
            if (!target_user || target_user < 0) {
                return mainStore.globalMessageTip('用户信息异常，请刷新页面!', 3);
            }
            if (!trade_no) {
                return mainStore.globalMessageTip('订单选择异常，请刷新页面!', 3);
            }
            if (!ticket_num || ticket_num < 0) {
                return mainStore.globalMessageTip('请输入正确的点券数量!', 3);
            }
            if (ticket_num > refundTicketForm.max_ticket) {
                return mainStore.globalMessageTip('退款的点券数量超过该订单充值数量!', 3);
            }
            if (ticket_num > refundTicketForm.user_ticket) {
                return mainStore.globalMessageTip('退款的点券数量超过用户剩余点券数量!', 3);
            }
            if (userID && identityToken) {
                mainStore.globalLoading(true);
                await requset.refundRechargeTicket({
                    userID: userID,
                    identityToken: identityToken,
                    authorityID: authorityID,
                    targetUser: target_user,
                    tradeNo: trade_no,
                    ticketNum: ticket_num,
                    token: token
                }).then(async res => {
                    if (res.code === 0) {
                        mainStore.globalMessageTip('处理成功', 0);
                        filterForm.tradeNo = trade_no;
                        await searchTradeNo();
                    } else {
                        if (!mainStore.dealResponseErrInfo(res.code)) {
                            switch (res.code) {
                                case 5:
                                    mainStore.globalMessageTip('订单不存在', 3);
                                    break;
                                case 6:
                                    mainStore.globalMessageTip('该订单没有交易成功', 3);
                                    break;
                                case 7:
                                    mainStore.globalMessageTip('退款点券数量超过订单充值点券数量', 3);
                                case 8:
                                    mainStore.globalMessageTip('用户信息错误，请联系周玉华', 3);
                                    break;
                                case 9:
                                    mainStore.globalMessageTip('退款点券数量超过用户剩余点券数量', 3);
                                    break;
                                case 10:
                                    mainStore.globalMessageTip('今日退款次数超过上限!', 3);
                                    break;
                                case 11:
                                    mainStore.globalMessageTip('该订单已退过款!', 3);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }).catch(err => {
                    mainStore.dealResponseErrInfo(-1);
                });
                mainStore.globalLoading(false);
                refundTicketDialog.value = false;
            } else {
                mainStore.dealResponseErrInfo(4);
            }
        } else {
            return;
        }
    });
}

function tradeTableRowClick(row: any, column: any, event: any) {
    tradeTableRef.value.toggleRowExpansion(row);
}



</script>

<template>
    <div class="account-search" v-if="mainStore.checkPermission(3002)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'service'
                }">
                    客服操作
                </el-breadcrumb-item>
                <el-breadcrumb-item>订单号查询</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 50%" fill>
                    <el-form ref="ruleFormRef" :model="filterForm" status-icon class="login-form">
                        <el-form-item prop="account" label="订单号" label-width="120px">
                            <el-input placeholder="请输入充值订单号" v-model="filterForm.tradeNo" size="large"
                                maxlength="255" />
                        </el-form-item>
                        <el-form-item prop="account" label-width="120px">
                            <el-button type="primary" size="large" @click="searchTradeNo()">确定查找</el-button>
                        </el-form-item>
                    </el-form>
                </el-space>
                <el-table :data="tradeInfo.data" border style="width: 100%" ref="tradeTableRef" @row-click="tradeTableRowClick">
                    <el-table-column type="expand">
                        <template #default="props">
                            <el-row>
                                <el-col :span="1"></el-col>
                                <el-col :span="2">购买记录: </el-col>
                                <el-col :span="21">
                                    <el-table :data="tradeInfo.buyGoodsRecord" style="width: 100%" border>
                                        <el-table-column prop="goods_id" label="商品ID" />
                                        <el-table-column prop="goods_name" label="商品名称" />
                                        <el-table-column prop="num" label="购买数量" />
                                        <el-table-column prop="pay_price" label="支付金额" />
                                        <el-table-column label="货币类型">
                                            <template #default="props">
                                                <span v-if="props.row.currency === 0">点券</span>
                                                <span v-else-if="props.row.currency === 1">金币</span>
                                                <span v-else>{{ props.row.currency }}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="buy_time" label="购买时间" />
                                    </el-table>
                                </el-col>
                            </el-row>
                        </template>
                    </el-table-column>
                    <el-table-column prop="user_id" label="用户ID" />
                    <el-table-column prop="account" label="账号" />
                    <el-table-column label="昵称">
                        <template #default="scope">
                            <el-link type="primary" :underline="false"
                                @click="mainStore.gotoUserDetail(scope.row.user_id)">{{ scope.row.nickname }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="left_ticket" label="剩余点券">
                    </el-table-column>
                    <el-table-column prop="left_coin" label="剩余金币">
                    </el-table-column>
                    <el-table-column prop="pay_channel" label="支付渠道">
                    </el-table-column>
                    <el-table-column prop="price" label="金额">
                        <template #default="scope">
                            <span>{{ scope.row.price / 100 }} </span>
                            <span> {{ scope.row.currency }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="tickets" label="充值点券">
                    </el-table-column>
                    <el-table-column prop="app_channel" label="平台渠道">
                    </el-table-column>
                    <el-table-column label="支付状态">
                        <template #default="scope">
                            <span v-if="scope.row.state > 0" style="color: #67C23A;">成功</span>
                            <span v-else style="color: #F56C6C;">失败</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="time" label="支付时间">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="danger" size="large" @click="refundTicket(scope.row)"
                                v-if="scope.row.state > 0 && scope.row.refund_flag === 0">退点券</el-button>
                            <span v-else-if="scope.row.state > 0 && scope.row.refund_flag === 1">已退款</span>
                            <span v-else>无需操作</span>
                        </template>
                    </el-table-column>
                </el-table>

            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="refundTicketDialog" title="点券扣除">
        <el-form :model="refundTicketForm" ref="refundTicketFormRef" status-icon :rules="refundTicketFormRules">
            <el-form-item label="订单号">
                <el-input v-model="refundTicketForm.trade_no" size="large" maxlength="8" disabled />
            </el-form-item>
            <el-form-item label="扣除点券" prop="ticket_num">
                <el-input placeholder="请输入点券扣除数量，不能超过充值所得值" v-model="refundTicketForm.ticket_num" size="large"
                    maxlength="8" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="refundTicketSubmit(refundTicketFormRef)" size="large">确定</el-button>
                <el-button @click="refundTicketDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped>

</style>