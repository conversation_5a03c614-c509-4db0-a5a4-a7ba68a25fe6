<script setup lang="ts">
import { ArrowRight, House, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules, UploadInstance } from 'element-plus';
import { ElMessageBox } from 'element-plus';
import { HomePageRecSavItem } from '../../interface';

const mainStore = useMainStore();

const addHomeSavDialog = ref(false);
const editHomeSavDialog = ref(false);
const loading = ref(false);

const homeSavData = reactive({
    data: [] as Array<HomePageRecSavItem>
});

const addHomeSavBtnLock = ref(true);
const addHomeSavForm = reactive({
    title: '',
    game_series: 0,
    game_id: 0,
    sav_ids: '',
    banner_url: '',
    banner_url_m: '',
    currency: 1,
    price: 0,
    origin_price: 0,
    is_show: 1,
});
const addHomeSavRuleFormRef = ref<FormInstance>();
const addHomeSavFormRules = reactive<FormRules>({
    game_id: [
        {
            required: true, message: '请选择游戏', trigger: 'blur'
        }
    ],
    sav_ids: [
        {
            required: true, message: '请输入录像ID', trigger: 'blur'
        }
    ],
    banner_url: [
        {
            required: true, message: '请上传PC版banner', trigger: 'blur'
        }
    ],
    banner_url_m: [
        {
            required: true, message: '请上传手机版banner', trigger: 'blur'
        }
    ],
    currency: [
        {
            required: true, message: '请选择货币类型', trigger: 'blur'
        }
    ],
    price: [
        {
            required: true, message: '请输入价格', trigger: 'blur'
        }
    ],
    origin_price: [
        {
            required: true, message: '请输入原价', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
});

const editHomeSavBtnLock = ref(true);
const editHomeSavForm = reactive({
    id: 0,
    title: '',
    game_series: 0,
    game_id: 0,
    sav_ids: '',
    banner_url: '',
    banner_url_m: '',
    currency: 1,
    price: 0,
    origin_price: 0,
    is_show: 1,
});
const editHomeSavRuleFormRef = ref<FormInstance>();
const editHomeSavFormRules = reactive<FormRules>({
    game_id: [
        {
            required: true, message: '请选择游戏', trigger: 'blur'
        }
    ],
    sav_ids: [
        {
            required: true, message: '请输入录像ID', trigger: 'blur'
        }
    ],
    banner_url: [
        {
            required: true, message: '请上传PC版banner', trigger: 'blur'
        }
    ],
    banner_url_m: [
        {
            required: true, message: '请上传手机版banner', trigger: 'blur'
        }
    ],
    currency: [
        {
            required: true, message: '请选择货币类型', trigger: 'blur'
        }
    ],
    price: [
        {
            required: true, message: '请输入价格', trigger: 'blur'
        }
    ],
    origin_price: [
        {
            required: true, message: '请输入原价', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
});

const editUploadRef = ref<UploadInstance>();
const editPhoneUploadRef = ref<UploadInstance>();

const addGameList = computed(() => {
    return mainStore.gameList.filter(item => item.series_id === addHomeSavForm.game_series);
});
const editGameList = computed(() => {
    return mainStore.gameList.filter(item => item.series_id === editHomeSavForm.game_series);
});


getHomePageRecSav();

async function getHomePageRecSav() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getHomePageRecSav({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.homeSavList) {
                    homeSavData.data = res.data.homeSavList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }

}

function addHomePageSav() {
    addHomeSavDialog.value = true;
}

async function addHomePageSavSubmit(formEl: FormInstance | undefined) {
    if (!addHomeSavBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (addHomeSavForm.title.length > 128) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (!addHomeSavForm.sav_ids) {
                return mainStore.globalMessageTip('请输入录像ID', 3);
            }
            if (!addHomeSavForm.banner_url) {
                return mainStore.globalMessageTip('请上传PC版图片', 3);
            }
            if (!addHomeSavForm.banner_url_m) {
                return mainStore.globalMessageTip('请上传手机版图片', 3);
            }
            if (addHomeSavForm.price < 0 || addHomeSavForm.origin_price < 0) {
                return mainStore.globalMessageTip('价格必须大于等于0', 3);
            }
            if (addHomeSavForm.price > addHomeSavForm.origin_price) {
                return mainStore.globalMessageTip('价格必须小于等于原价', 3);
            }
            if (!mainStore.recSavCurrency.some(item => item.id === addHomeSavForm.currency)) {
                return mainStore.globalMessageTip('非法的货币类型', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${addHomeSavForm.game_id}${addHomeSavForm.sav_ids}${addHomeSavForm.currency}${addHomeSavForm.price}${addHomeSavForm.origin_price}${addHomeSavForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            addHomeSavBtnLock.value = false;
            await requset.addHomePageRecSav({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                title: addHomeSavForm.title,
                gameID: addHomeSavForm.game_id,
                savIds: addHomeSavForm.sav_ids,
                iconUrl1: addHomeSavForm.banner_url,
                iconUrl2: addHomeSavForm.banner_url_m,
                currency: addHomeSavForm.currency,
                price: addHomeSavForm.price,
                origin_price: addHomeSavForm.origin_price,
                is_show: addHomeSavForm.is_show,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('添加成功!', 0);
                    addHomeSavDialog.value = false;
                    addHomeSavForm.banner_url = '';
                    addHomeSavForm.banner_url_m = '';
                    addHomeSavForm.title = '';
                    addHomeSavForm.sav_ids = '';
                    addHomeSavForm.game_id = 0;
                    addHomeSavForm.currency = 1;
                    addHomeSavForm.price = 0;
                    addHomeSavForm.origin_price = 0;
                    addHomeSavForm.is_show = 1;
                    getHomePageRecSav();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('推荐录像数量已达上限!', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
                mainStore.globalLoading(false);
                addHomeSavBtnLock.value = true;
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
                mainStore.globalLoading(false);
                addHomeSavBtnLock.value = true;
            });
        } else {
            return;
        }
    });
}

function editHomePageSav(item: HomePageRecSavItem) {
    const gameSeriesIndex = mainStore.gameList.findIndex(item2 => item2.game_id === item.sav_game_id);
    if (gameSeriesIndex >= 0) {
        editHomeSavForm.game_series = mainStore.gameList[gameSeriesIndex].series_id;
    }
    editUploadRef.value?.clearFiles();
    editPhoneUploadRef.value?.clearFiles();
    editHomeSavForm.game_id = item.sav_game_id;
    editHomeSavForm.id = item.id;
    editHomeSavForm.banner_url = item.icon_url_1;
    editHomeSavForm.banner_url_m = item.icon_url_2;
    editHomeSavForm.sav_ids = item.sav_ids;
    editHomeSavForm.title = item.title;
    editHomeSavForm.currency = item.currency;
    editHomeSavForm.price = item.price;
    editHomeSavForm.origin_price = item.origin_price;
    editHomeSavForm.is_show = item.is_show;

    editHomeSavDialog.value = true;

}

async function deleteHomePageSav(id: number) {
    if (!id) {
        return;
    }
    ElMessageBox.confirm(`确定要删除该推荐录像?`, '删除推荐录像', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.deleteHomePageRecSav({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetID: id,
            token: token
        }).then((res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('删除成功!', 0);
                getHomePageRecSav();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该推荐录像不存在!', 3);
                            getHomePageRecSav();
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });

}


async function editHomePageSavSubmit(formEl: FormInstance | undefined) {
    if (!editHomeSavBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (editHomeSavForm.title.length > 128) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (!editHomeSavForm.banner_url) {
                return mainStore.globalMessageTip('请上传PC版图片', 3);
            }
            if (!editHomeSavForm.banner_url_m) {
                return mainStore.globalMessageTip('请上传手机版图片', 3);
            }
            if (!editHomeSavForm.sav_ids) {
                return mainStore.globalMessageTip('请输入录像ID', 3);
            }
            if (editHomeSavForm.price < 0 || editHomeSavForm.origin_price < 0) {
                return mainStore.globalMessageTip('价格必须大于等于0', 3);
            }
            if (editHomeSavForm.price > editHomeSavForm.origin_price) {
                return mainStore.globalMessageTip('价格必须小于等于原价', 3);
            }
            if (!mainStore.recSavCurrency.some(item => item.id === editHomeSavForm.currency)) {
                return mainStore.globalMessageTip('非法的货币类型', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${editHomeSavForm.id}${editHomeSavForm.game_id}${editHomeSavForm.sav_ids}${editHomeSavForm.currency}${editHomeSavForm.price}${editHomeSavForm.origin_price}${editHomeSavForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            editHomeSavBtnLock.value = false;
            await requset.editHomePageRecSav({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetID: editHomeSavForm.id,
                title: editHomeSavForm.title,
                gameID: editHomeSavForm.game_id,
                savIds: editHomeSavForm.sav_ids,
                iconUrl1: editHomeSavForm.banner_url,
                iconUrl2: editHomeSavForm.banner_url_m,
                currency: editHomeSavForm.currency,
                price: editHomeSavForm.price,
                origin_price: editHomeSavForm.origin_price,
                is_show: editHomeSavForm.is_show,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('修改成功!', 0);
                    editHomeSavDialog.value = false;
                    editHomeSavForm.id = 0;
                    editHomeSavForm.banner_url = '';
                    editHomeSavForm.banner_url_m = '';
                    editHomeSavForm.title = '';
                    editHomeSavForm.sav_ids = '';
                    editHomeSavForm.game_id = 0;
                    editHomeSavForm.currency = 1;
                    editHomeSavForm.price = 0;
                    editHomeSavForm.origin_price = 0;
                    editHomeSavForm.is_show = 1;
                    editUploadRef.value?.clearFiles();
                    editPhoneUploadRef.value?.clearFiles();
                    getHomePageRecSav();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该推荐录像不存在!', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            editHomeSavBtnLock.value = true;
        } else {
            return;
        }
    });
}

function bannerUploadBefore(file: any) {
    console.log(file);
    if (file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'image/jpeg') {
        mainStore.globalMessageTip('只能上传PNG/JPG类型图片', 3);
        return false;
    }
    if (file.size > 5120000) {
        mainStore.globalMessageTip('文件太大了', 3);
        return false;
    }
}

function bannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                addHomeSavForm.banner_url = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function phoneBannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                addHomeSavForm.banner_url_m = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function editBannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                editHomeSavForm.banner_url = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function editPhoneBannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                editHomeSavForm.banner_url_m = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function bannerUploadError(err: any) {
    mainStore.globalMessageTip(err, 3);
}

function addSelectGameSeries(val: number) {
    const gameIndex = mainStore.gameList.findIndex(item => item.series_id === val);
    if (gameIndex >= 0) {
        addHomeSavForm.game_id = mainStore.gameList[gameIndex].game_id;
    }
}

function editSelectGameSeries(val: number) {
    const gameIndex = mainStore.gameList.findIndex(item => item.series_id === val);
    if (gameIndex >= 0) {
        editHomeSavForm.game_id = mainStore.gameList[gameIndex].game_id;
    }
}

async function sortHomePageRecSav(id: number, type: number) {
    if (!id) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${id}${type}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    mainStore.globalLoading(true);
    await requset.sortHomePageRecSav({
        userID: userID,
        identityToken: identityToken,
        authorityID: authorityID,
        targetID: id,
        sortType: type,
        token: token
    }).then((res) => {
        if (res.code === 0) {
            mainStore.globalMessageTip('修改成功!', 0);
            getHomePageRecSav();
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                switch (res.code) {
                    case 5:
                    case 6:
                        mainStore.globalMessageTip('记录已过时, 正在刷新...', 3);
                        getHomePageRecSav();
                        break;
                    default:
                        break;
                }
            }
        }
    }).catch((err) => {
        mainStore.dealResponseErrInfo(-1);
    });
    mainStore.globalLoading(false);
    editHomeSavBtnLock.value = true;
}


</script>

<template>
    <div class="banner-config" v-if="mainStore.checkPermission(2003)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'inside'
                }">
                    约战内部
                </el-breadcrumb-item>
                <el-breadcrumb-item>平台推荐录像</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-button type="primary" size="large" @click="addHomePageSav()">添加推荐录像
                                </el-button>
                                <el-button type="primary" size="large" @click="getHomePageRecSav()">刷新
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table v-loading="loading" :data="homeSavData.data" border style="width: 100%">
                        <el-table-column prop="id" label="ID" />
                        <el-table-column prop="title" label="标题" />
                        <el-table-column label="游戏">
                            <template #default="scope">
                                <span>{{ mainStore.getGameName(scope.row.sav_game_id) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="PC图片">
                            <template #default="scope">
                                <el-image fit="contain" :src="scope.row.icon_url_1">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                            </template>
                        </el-table-column>
                        <el-table-column label="手机图片">
                            <template #default="scope">
                                <el-image fit="contain" :src="scope.row.icon_url_2">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                            </template>
                        </el-table-column>
                        <el-table-column prop="price" label="价格" />
                        <el-table-column prop="origin_price" label="原价" />
                        <!-- <el-table-column prop="sav_ids" label="录像ID" /> -->
                        <el-table-column prop="update_time" label="更新时间" />
                        <el-table-column label="移动">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="primary"
                                        :disabled="scope.row.id === homeSavData.data[0].id ? true : false"
                                        @click="sortHomePageRecSav(scope.row.id, 1)">置顶</el-link>
                                    <el-link type="danger"
                                        :disabled="scope.row.id === homeSavData.data[0].id ? true : false"
                                        @click="sortHomePageRecSav(scope.row.id, 2)">上移</el-link>
                                    <el-link type="warning"
                                        :disabled="scope.row.id === homeSavData.data[homeSavData.data.length - 1].id ? true : false"
                                        @click="sortHomePageRecSav(scope.row.id, 3)">
                                        下移</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="primary" @click="editHomePageSav(scope.row)">编辑</el-link>
                                    <el-link type="info" @click="deleteHomePageSav(scope.row.id)">删除</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="addHomeSavDialog" title="添加推荐录像">
        <el-form :model="addHomeSavForm" :rules="addHomeSavFormRules" ref="addHomeSavRuleFormRef" class="add-banner-form"
            status-icon>
            <el-form-item label="标题">
                <el-input placeholder="请输入标题(可选)" v-model="addHomeSavForm.title" type="text" maxlength="128" size="large" />
            </el-form-item>
            <el-form-item label="游戏" prop="game_id" required>
                <el-space wrap>
                    <el-select v-model="addHomeSavForm.game_series" placeholder="请选择游戏系列" size="large"
                        @change="addSelectGameSeries">
                        <el-option v-for="(item, index) in mainStore.gameSeries" :key="item.series_id"
                            :label="item.series_name" :value="item.series_id" />
                    </el-select>
                    <el-select v-model="addHomeSavForm.game_id" placeholder="请选择游戏" size="large">
                        <el-option v-for="item in addGameList" :key="item.game_id" :label="item.game_name"
                            :value="item.game_id" />
                    </el-select>
                </el-space>
            </el-form-item>
            <el-form-item label="录像ID" prop="sav_ids" required>
                <el-input type="textarea" v-model="addHomeSavForm.sav_ids" :rows="4" placeholder="请输入录像ID" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="货币" prop="currency" required>
                <el-select v-model="addHomeSavForm.currency" placeholder="请选择货币" size="large" disabled>
                    <el-option v-for="(item, index) in mainStore.recSavCurrency" :key="item.id" :label="item.name"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="价格" prop="price" required>
                <el-input v-model="addHomeSavForm.price" :rows="4" placeholder="请输入价格" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="原价" prop="origin_price" required>
                <el-input v-model="addHomeSavForm.origin_price" :rows="4" placeholder="请输入原价" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="PC图片" required>
                <el-upload class="upload-demo" drag accept=".jpeg, .png, .jpg" :action="requset.uploadFileApi" :data="{
                    userID: mainStore.userInfo.user_id,
                    identityToken: mainStore.identityToken,
                    token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                }" :limit="1" :before-upload="bannerUploadBefore" :on-success="bannerUploadSuccess"
                    :on-error="bannerUploadError" size="large">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%">
                    <el-image fit="contain" :src="addHomeSavForm.banner_url">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="手机图片" required>
                <el-upload class="upload-demo" drag accept=".jpeg, .png, .jpg" :action="requset.uploadFileApi" :data="{
                    userID: mainStore.userInfo.user_id,
                    identityToken: mainStore.identityToken,
                    token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                }" :limit="1" :before-upload="bannerUploadBefore" :on-success="phoneBannerUploadSuccess"
                    :on-error="bannerUploadError">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%">
                    <el-image fit="contain" :src="addHomeSavForm.banner_url_m">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="展示" required>
                <el-switch v-model="addHomeSavForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addHomePageSavSubmit(addHomeSavRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="addHomeSavDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>

    <el-dialog v-model="editHomeSavDialog" title="编辑推荐录像">
        <el-form :model="editHomeSavForm" :rules="editHomeSavFormRules" ref="editHomeSavRuleFormRef"
            class="edit-banner-form" status-icon>
            <el-form-item label="ID" prop="id" required>
                <el-input placeholder="ID" v-model="editHomeSavForm.id" type="text" disabled />
            </el-form-item>
            <el-form-item label="标题">
                <el-input placeholder="请输入标题(可选)" v-model="editHomeSavForm.title" type="text" maxlength="128" />
            </el-form-item>
            <el-form-item label="游戏" prop="game_id" required>
                <el-space wrap>
                    <el-select v-model="editHomeSavForm.game_series" placeholder="请选择游戏系列" size="large"
                        @change="editSelectGameSeries">
                        <el-option v-for="(item, index) in mainStore.gameSeries" :key="item.series_id"
                            :label="item.series_name" :value="item.series_id" />
                    </el-select>
                    <el-select v-model="editHomeSavForm.game_id" placeholder="请选择游戏" size="large">
                        <el-option v-for="item in editGameList" :key="item.game_id" :label="item.game_name"
                            :value="item.game_id" />
                    </el-select>
                </el-space>

            </el-form-item>
            <el-form-item label="录像ID" prop="sav_ids" required>
                <el-input type="textarea" v-model="editHomeSavForm.sav_ids" :rows="4" placeholder="请输入录像ID"></el-input>
            </el-form-item>
            <el-form-item label="货币" prop="currency" required>
                <el-select v-model="editHomeSavForm.currency" placeholder="请选择货币" size="large" disabled>
                    <el-option v-for="(item, index) in mainStore.recSavCurrency" :key="item.id" :label="item.name"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="价格" prop="price" required>
                <el-input v-model="editHomeSavForm.price" :rows="4" placeholder="请输入价格" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="原价" prop="origin_price" required>
                <el-input v-model="editHomeSavForm.origin_price" :rows="4" placeholder="请输入原价" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="PC图片" required>
                <el-upload class="upload-demo" ref="editUploadRef" drag accept=".jpeg, .png, .jpg"
                    :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :limit="1" :before-upload="bannerUploadBefore" :on-success="editBannerUploadSuccess"
                    :on-error="bannerUploadError">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%;">
                    <el-image fit="contain" :src="editHomeSavForm.banner_url">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="手机图片" required>
                <el-upload class="upload-demo" ref="editPhoneUploadRef" drag accept=".jpeg, .png, .jpg"
                    :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :limit="1" :before-upload="bannerUploadBefore" :on-success="editPhoneBannerUploadSuccess"
                    :on-error="bannerUploadError">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block">
                    <el-image fit="contain" :src="editHomeSavForm.banner_url_m">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="展示" required>
                <el-switch v-model="editHomeSavForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="editHomePageSavSubmit(editHomeSavRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="editHomeSavDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped></style>