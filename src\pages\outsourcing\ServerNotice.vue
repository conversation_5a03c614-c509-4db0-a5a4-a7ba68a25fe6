<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const currentPage = ref(1);

const filterForm = reactive({
    notice: '',
});

const filterFormRef = ref<FormInstance>();
const filterFormRules = reactive<FormRules>({
    notice: [
        {
            required: true, message: '请输入公告内容', trigger: 'blur'
        }
    ],
});

const noticeTemplate = reactive({
    tpl1: '"比赛"正在 拳皇97>电信一区>4号桌 火热进行,欢迎大家前往观战.',
    tpl2: '服务器即将升级维护,持续时间大概为10-15分钟,给您带来不便,敬请谅解.'
})

getServerNoticeList();

async function getServerNoticeList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${currentPage.value}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;

        await requset.getServerNoticeList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            currentPage: currentPage.value,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    outsourcingStore.serverNoticeTotal = res.data.total;
                    outsourcingStore.serverNoticeList = res.data.serverNoticeList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function sendServerNotice(formEl: FormInstance | undefined) {
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto.SHA1(`${userID}${authorityID}${filterForm.notice}${requset.ADMIN_TOKEN_KEY}`).toString();
            if (userID && identityToken) {
                mainStore.globalLoading(true);
                await requset.sendServerNotice({
                    userID: userID,
                    identityToken: identityToken,
                    authorityID: authorityID,
                    msg: filterForm.notice,
                    token: token
                }).then(res => {
                    if (res.code === 0) {
                        mainStore.globalMessageTip('发送成功', 0);
                    } else {
                        if (!mainStore.dealResponseErrInfo(res.code)) {
                            switch (res.code) {
                                case 5:
                                    mainStore.globalMessageTip('含有敏感字段', 3);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }).catch(err => {
                    console.log(err);
                });
                mainStore.globalLoading(false);
            } else {
                mainStore.dealResponseErrInfo(4);
            }
        } else {
            return;
        }
    });

}

async function currentPageChange(page: any) {
    currentPage.value = page;
    await getServerNoticeList();
}



</script>
    
<template>
    <div class="kick-player" v-if="mainStore.checkPermission(1002)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'outsourcing'
                }">
                    外协管理
                </el-breadcrumb-item>
                <el-breadcrumb-item>全区公告</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill size="large">
                    <el-form ref="filterFormRef" :model="filterForm" status-icon :rules="filterFormRules"
                        :label-position="'top'" class="filter-form">
                        <el-form-item label="公告功能" prop="notice">
                            <el-input placeholder="请输入公告内容" v-model="filterForm.notice" size="large" maxlength="1000"
                                :input-style="{width: 'auto',minWidth: '50%'}" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="sendServerNotice(filterFormRef)" size="large">发送全服公告
                            </el-button>
                        </el-form-item>
                        <el-row>
                            <p>常用模板</p>
                        </el-row>
                        <el-space direction="vertical" style="width: 100%" fill size="large">
                            <el-row>
                                <el-col :span="12">
                                    <el-input v-model="noticeTemplate.tpl1" size="large" maxlength="1000"
                                        :input-style="{width: 'auto',minWidth: '95%'}" />
                                </el-col>
                                <el-col :span="12">
                                    <el-button type="primary" @click="mainStore.copyContent(noticeTemplate.tpl1)"
                                        size="large">
                                        复制
                                    </el-button>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="12">
                                    <el-input v-model="noticeTemplate.tpl2" size="large" maxlength="1000"
                                        :input-style="{width: 'auto',minWidth: '95%'}" />
                                </el-col>
                                <el-col :span="12">
                                    <el-button type="primary" @click="mainStore.copyContent(noticeTemplate.tpl2)"
                                        size="large">
                                        复制
                                    </el-button>
                                </el-col>
                            </el-row>
                        </el-space>

                    </el-form>
                    <el-table :data="outsourcingStore.serverNoticeList" border style="width: 100%">
                        <el-table-column prop="msg" label="公告内容" />
                        <el-table-column prop="time" label="发布时间" />
                    </el-table>
                    <el-pagination background layout="prev, pager, next" :total="outsourcingStore.serverNoticeTotal"
                        :current-page="currentPage" @current-change="currentPageChange" />
                </el-space>

            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>
    
<style lang="less" scoped>

</style>