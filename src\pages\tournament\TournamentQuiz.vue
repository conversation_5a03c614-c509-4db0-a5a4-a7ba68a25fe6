<script setup lang="ts">
import { ArrowRight, House, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useRoute } from 'vue-router';
import { useMainStore } from '../../store';
import { useTournamentStore } from '../../store/tournament';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox, genFileId } from 'element-plus';
import { TournamentItem, TournamentQuizItem, TournamentQuizOptionForm, TournamentQuizOptionItem } from '../../interface';
import { number } from 'echarts/core';

const mainStore = useMainStore();
const tournamentStore = useTournamentStore();

const route = useRoute();

const loading = ref(false)
const addTournamentQuizDialog = ref(false);
const editTournamentQuizDialog = ref(false);
const addTournamentQuizOptionDialog = ref(false);
const editTournamentQuizOptionDialog = ref(false);
const currentTournament = ref('' as string | number);
const quizDataTableRef = ref();
const queryTournamentId = ref(0);

if (route.query && route.query.id) {
    queryTournamentId.value = parseInt(String(route.query.id));
}

const fetchFlag = reactive({
    tournamentList: false,
    tournamentQuiz: false
});

const tournament = reactive({
    list: [] as Array<TournamentItem>,
    quiz: [] as Array<TournamentQuizItem>
});

const addTournamentQuizBtnLock = ref(true);
const addTournamentQuizForm = reactive({
    competition_id: '' as string | number,
    title: '',
    content: '',
    stage: '',
    start_time: '',
    end_time: '',
    is_show: 1,
    options: [
        {
            id: 1,
            option_name: '',
            is_show: 1
        },
        {
            id: 2,
            option_name: '',
            is_show: 1
        },
    ] as Array<TournamentQuizOptionForm>,
    time_range: [] as Array<string>
});
const addTournamentQuizRuleFormRef = ref<FormInstance>();
const addTournamentQuizFormRules = reactive<FormRules>({
    competition_id: [
        {
            required: true, message: '请选择赛事', trigger: 'blur'
        }
    ],
    title: [
        {
            required: true, message: '请输入标题', trigger: 'blur'
        }
    ],
    content: [
        {
            required: true, message: '请输入竞猜内容', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
    time_range: [
        {
            required: true, message: '请选择时间范围', trigger: 'blur'
        }
    ],
});

const editTournamentQuizBtnLock = ref(true);
const editTournamentQuizForm = reactive({
    quiz_id: 0,
    competition_id: 0,
    title: '',
    content: '',
    stage: '',
    start_time: '',
    end_time: '',
    is_show: 1,
    time_range: [] as Array<string>
});
const editTournamentQuizRuleFormRef = ref<FormInstance>();
const editTournamentQuizFormRules = reactive<FormRules>({
    title: [
        {
            required: true, message: '请输入标题', trigger: 'blur'
        }
    ],
    content: [
        {
            required: true, message: '请输入竞猜内容', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
    time_range: [
        {
            required: true, message: '请选择时间范围', trigger: 'blur'
        }
    ],
});

const addTournamentQuizOptionBtnLock = ref(true);
const addTournamentQuizOptionForm = reactive({
    quiz_id: 0,
    option_name: '',
});
const addTournamentQuizOptionRuleFormRef = ref<FormInstance>();
const addTournamentQuizOptionFormRules = reactive<FormRules>({
    option_name: [
        {
            required: true, message: '请输入选项内容', trigger: 'blur'
        }
    ],
});

const editTournamentQuizOptionBtnLock = ref(true);
const editTournamentQuizOptionForm = reactive({
    option_id: 0,
    quiz_id: 0,
    option_name: '',
});
const editTournamentQuizOptionRuleFormRef = ref<FormInstance>();
const editTournamentQuizOptionFormRules = reactive<FormRules>({
    option_name: [
        {
            required: true, message: '请输入选项内容', trigger: 'blur'
        }
    ],
});

const tournamentQuizSort = computed(() => {
    const arr1 = tournament.quiz.filter(item => item.quiz_state === 2);
    const arr2 = tournament.quiz.filter(item => item.quiz_state === 1);
    const arr3 = tournament.quiz.filter(item => item.quiz_state === 0);
    const arr4 = tournament.quiz.filter(item => item.quiz_state === 3);
    return arr1.concat(arr2, arr3, arr4);
});

getTournamentList();


async function getTournamentList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    if (userID && identityToken) {
        fetchFlag.tournamentList = false;
        await requset.getTournamentList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.tournamentList) {
                    tournament.list = res.data.tournamentList;
                    if (tournament.list.length > 0) {
                        if (queryTournamentId.value > 0) {
                            currentTournament.value = queryTournamentId.value;
                            queryTournamentId.value = 0;
                        } else {
                            currentTournament.value = tournament.list[0].id;
                        }
                        getTournamentQuizList();
                    }
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
    fetchFlag.tournamentList = true;
}

async function getTournamentQuizList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${currentTournament.value}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    if (userID && identityToken) {
        fetchFlag.tournamentQuiz = false;
        await requset.getTournamentQuizList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            competitionID: parseInt(String(currentTournament.value)),
            token: token,
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.tournamentQuizList) {
                    tournament.quiz = res.data.tournamentQuizList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
    fetchFlag.tournamentQuiz = true;
}


function addTournamentQuiz() {
    addTournamentQuizForm.competition_id = currentTournament.value;
    addTournamentQuizDialog.value = true;
}

async function addTournamentQuizSubmit(formEl: FormInstance | undefined) {
    if (!addTournamentQuizBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (!addTournamentQuizForm.competition_id) {
                return mainStore.globalMessageTip('请选择赛事', 3);
            }
            if (addTournamentQuizForm.title.length > 20) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (addTournamentQuizForm.title.length > 128) {
                return mainStore.globalMessageTip('内容过长', 3);
            }
            if (addTournamentQuizForm.stage.length > 10) {
                return mainStore.globalMessageTip('阶段文本过长', 3);
            }
            if (addTournamentQuizForm.time_range.length !== 2) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            } else {
                addTournamentQuizForm.start_time = addTournamentQuizForm.time_range[0];
                addTournamentQuizForm.end_time = addTournamentQuizForm.time_range[1];
            }
            if (!addTournamentQuizForm.start_time || !addTournamentQuizForm.end_time) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            }
            if (addTournamentQuizForm.options.length < 2) {
                return mainStore.globalMessageTip('至少需要2个竞猜选项', 3);
            } else {
                let emptyOption = 0;
                addTournamentQuizForm.options.forEach(item => {
                    if (item.option_name.length > 0) {
                        emptyOption += 1;
                    }
                });
                if (emptyOption < 2) {
                    return mainStore.globalMessageTip('至少需要2个竞猜选项', 3);
                }
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${addTournamentQuizForm.competition_id}${addTournamentQuizForm.start_time}${addTournamentQuizForm.end_time}${addTournamentQuizForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            addTournamentQuizBtnLock.value = false;
            await requset.addTournamentQuiz({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                title: addTournamentQuizForm.title,
                competitionID: parseInt(String(addTournamentQuizForm.competition_id)),
                content: addTournamentQuizForm.content,
                stage: addTournamentQuizForm.stage,
                isShow: addTournamentQuizForm.is_show,
                options: addTournamentQuizForm.options,
                startTime: addTournamentQuizForm.start_time,
                endTime: addTournamentQuizForm.end_time,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('添加成功!', 0);
                    addTournamentQuizDialog.value = false;
                    addTournamentQuizForm.options = [
                        {
                            id: 1,
                            option_name: '',
                            is_show: 1
                        },
                        {
                            id: 2,
                            option_name: '',
                            is_show: 1
                        },
                    ];
                    addTournamentQuizForm.stage = '';
                    addTournamentQuizForm.content = '';
                    addTournamentQuizForm.title = '';
                    addTournamentQuizForm.start_time = '';
                    addTournamentQuizForm.end_time = '';
                    addTournamentQuizForm.time_range = [];
                    addTournamentQuizForm.competition_id = '';
                    addTournamentQuizForm.is_show = 1;
                    getTournamentQuizList();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该赛事不存在!', 3);
                                getTournamentList();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            addTournamentQuizBtnLock.value = true;
        } else {
            return;
        }
    });
}


function editTournamentQuiz(item: TournamentQuizItem) {
    editTournamentQuizForm.competition_id = item.competition_id;
    editTournamentQuizForm.quiz_id = item.quiz_id;
    editTournamentQuizForm.title = item.quiz_title;
    editTournamentQuizForm.content = item.quiz_content;
    editTournamentQuizForm.start_time = item.start_time;
    editTournamentQuizForm.end_time = item.end_time;
    editTournamentQuizForm.stage = item.stage;
    editTournamentQuizForm.is_show = item.is_show;
    editTournamentQuizForm.time_range = [item.start_time, item.end_time];
    editTournamentQuizDialog.value = true;
}

async function editTournamentQuizSubmit(formEl: FormInstance | undefined) {
    if (!editTournamentQuizBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (editTournamentQuizForm.title.length > 20) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (editTournamentQuizForm.title.length > 128) {
                return mainStore.globalMessageTip('内容过长', 3);
            }
            if (editTournamentQuizForm.stage.length > 10) {
                return mainStore.globalMessageTip('阶段文本过长', 3);
            }
            if (editTournamentQuizForm.time_range.length !== 2) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            } else {
                editTournamentQuizForm.start_time = editTournamentQuizForm.time_range[0];
                editTournamentQuizForm.end_time = editTournamentQuizForm.time_range[1];
            }
            if (!editTournamentQuizForm.start_time || !editTournamentQuizForm.end_time) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${editTournamentQuizForm.quiz_id}${editTournamentQuizForm.start_time}${editTournamentQuizForm.end_time}${editTournamentQuizForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            editTournamentQuizBtnLock.value = false;
            await requset.editTournamentQuiz({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                title: editTournamentQuizForm.title,
                quizID: editTournamentQuizForm.quiz_id,
                content: editTournamentQuizForm.content,
                stage: editTournamentQuizForm.stage,
                isShow: editTournamentQuizForm.is_show,
                startTime: editTournamentQuizForm.start_time,
                endTime: editTournamentQuizForm.end_time,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('修改成功!', 0);
                    editTournamentQuizDialog.value = false;
                    editTournamentQuizForm.stage = '';
                    editTournamentQuizForm.content = '';
                    editTournamentQuizForm.title = '';
                    editTournamentQuizForm.start_time = '';
                    editTournamentQuizForm.end_time = '';
                    editTournamentQuizForm.time_range = [];
                    editTournamentQuizForm.quiz_id = 0;
                    editTournamentQuizForm.competition_id = 0;
                    editTournamentQuizForm.is_show = 1;
                    getTournamentQuizList();
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            editTournamentQuizBtnLock.value = true;
        } else {
            return;
        }
    });
}

async function deleteTournamentQuiz(quiz_id: number) {
    if (!quiz_id) {
        return;
    }
    ElMessageBox.confirm(`⚠警告: 确定要删除该竞猜? 如非特殊情况请勿进行删除操作`, '删除竞猜', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${quiz_id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.deleteTournamentQuiz({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetID: quiz_id,
            token: token
        }).then((res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('删除成功!', 0);
                getTournamentQuizList();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该竞猜不存在!', 3);
                            getTournamentQuizList();
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });
}


function addTournamentQuizFormOption() {
    addTournamentQuizForm.options.push({
        id: addTournamentQuizForm.options[addTournamentQuizForm.options.length - 1].id + 1,
        option_name: '',
        is_show: 1
    });
}

function deleteTournamentQuizFormOption(id: number) {
    const index = addTournamentQuizForm.options.findIndex(item => item.id === id);
    if (index >= 0) {
        addTournamentQuizForm.options.splice(index, 1);
    }
}

function addTournamentQuizOption(quiz_id: number) {
    addTournamentQuizOptionForm.quiz_id = quiz_id;
    addTournamentQuizOptionDialog.value = true;
}

async function addTournamentQuizOptionSubmit(formEl: FormInstance | undefined) {
    if (!addTournamentQuizOptionBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (addTournamentQuizOptionForm.option_name.length > 11) {
                return mainStore.globalMessageTip('选项内容过长', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${addTournamentQuizOptionForm.quiz_id}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            addTournamentQuizOptionBtnLock.value = false;
            await requset.addTournamentQuizOption({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                quizID: addTournamentQuizOptionForm.quiz_id,
                optionName: addTournamentQuizOptionForm.option_name,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('添加成功!', 0);
                    addTournamentQuizOptionDialog.value = false;
                    addTournamentQuizOptionForm.option_name = '';
                    addTournamentQuizOptionForm.quiz_id = 0;
                    getTournamentQuizList();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该竞猜不存在!', 3);
                                getTournamentQuizList();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            addTournamentQuizOptionBtnLock.value = true;
        } else {
            return;
        }
    });
}

function editTournamentQuizOption(item: TournamentQuizOptionItem) {
    editTournamentQuizOptionForm.option_id = item.option_id;
    editTournamentQuizOptionForm.quiz_id = item.quiz_id;
    editTournamentQuizOptionForm.option_name = item.option_name;
    editTournamentQuizOptionDialog.value = true;
}

async function editTournamentQuizOptionSubmit(formEl: FormInstance | undefined) {
    if (!editTournamentQuizOptionBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (editTournamentQuizOptionForm.option_name.length > 11) {
                return mainStore.globalMessageTip('选项内容过长', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${editTournamentQuizOptionForm.quiz_id}${editTournamentQuizOptionForm.option_id}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            editTournamentQuizOptionBtnLock.value = false;
            await requset.editTournamentQuizOption({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                optionID: editTournamentQuizOptionForm.option_id,
                quizID: editTournamentQuizOptionForm.quiz_id,
                optionName: editTournamentQuizOptionForm.option_name,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('修改成功!', 0);
                    editTournamentQuizOptionDialog.value = false;
                    editTournamentQuizOptionForm.option_name = '';
                    editTournamentQuizOptionForm.quiz_id = 0;
                    editTournamentQuizOptionForm.option_id = 0;
                    getTournamentQuizList();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该选项不存在!', 3);
                                getTournamentQuizList();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            editTournamentQuizOptionBtnLock.value = true;
        } else {
            return;
        }
    });
}

async function deleteTournamentQuizOption(quiz_id: number, option_id: number) {
    if (!quiz_id || !option_id) {
        return;
    }
    ElMessageBox.confirm(`⚠警告: 确定要删除该选项? 如非特殊情况请勿进行删除操作`, '删除竞猜选项', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${quiz_id}${option_id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.deleteTournamentQuizOption({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            quizID: quiz_id,
            optionID: option_id,
            token: token
        }).then((res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('删除成功!', 0);
                getTournamentQuizList();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该选项不存在!', 3);
                            getTournamentQuizList();
                            break;
                        case 6:
                            mainStore.globalMessageTip('至少需要保留2个选项!', 3);
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });
}

async function tournamentQuizSettlement(quiz_id: number, option_id: number, option_name: string) {
    if (!quiz_id || !option_id) {
        return;
    }
    ElMessageBox.confirm(`请再次确认结算选项: ${option_name}`, '竞猜结算', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${quiz_id}${option_id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.tournamentQuizSettlement({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            quizID: quiz_id,
            optionID: option_id,
            token: token
        }).then((res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('结算成功!', 0);
                getTournamentQuizList();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该竞猜不存在!', 3);
                            getTournamentQuizList();
                            break;
                        case 6:
                            mainStore.globalMessageTip('竞猜未开始!', 3);
                            getTournamentQuizList();
                            break;
                        case 7:
                            mainStore.globalMessageTip('该竞猜已结算!', 3);
                            getTournamentQuizList();
                            break;
                        case 8:
                            mainStore.globalMessageTip('竞猜未结束!', 3);
                            getTournamentQuizList();
                            break;
                        case 9:
                            mainStore.globalMessageTip('该选项不存在!', 3);
                            getTournamentQuizList();
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });
}




function selectTournament(id: number) {
    currentTournament.value = id;
    getTournamentQuizList();
}

function getCurrentTournamentLabel() {
    let label = '';
    tournament.list.some(item => {
        if (item.id === currentTournament.value) {
            label = item.title;
            return true;
        }
    });
    return label;
}

function reportTableRowClick(row: any, column: any, event: any) {
    quizDataTableRef.value.toggleRowExpansion(row);
}

</script>

<template>
    <div class="tournament-quiz" v-if="mainStore.checkPermission(6002)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'tournament'
                }">
                    赛事中心
                </el-breadcrumb-item>
                <el-breadcrumb-item>竞猜管理</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="!fetchFlag.tournamentList || !fetchFlag.tournamentQuiz">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-select v-model="currentTournament" placeholder="请选择赛事" size="large"
                                    @change="selectTournament">
                                    <el-option v-for="(item, index) in tournament.list" :key="item.id" :label="item.title"
                                        :value="item.id" />
                                </el-select>
                                <el-button type="primary" size="large" @click="addTournamentQuiz()">添加竞猜
                                </el-button>
                                <el-button type="primary" size="large" @click="getTournamentQuizList()">刷新
                                </el-button>
                                <el-text class="mx-1" size="large">当前选择：{{ getCurrentTournamentLabel() }}</el-text>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table v-loading="loading" :data="tournamentQuizSort" border style="width: 100%" toggleRowExpansion
                        ref="quizDataTableRef" @row-click="reportTableRowClick">
                        <el-table-column type="expand">
                            <template #default="scope">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-space size="large" direction="vertical" style="width: 100%;" fill>
                                            <el-row>
                                                <el-col :span="2">赛事: </el-col>
                                                <el-col :span="22">{{ getCurrentTournamentLabel() }}</el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">阶段: </el-col>
                                                <el-col :span="22">{{ scope.row.stage }}</el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">标题: </el-col>
                                                <el-col :span="22">{{ scope.row.quiz_title }}</el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">内容: </el-col>
                                                <el-col :span="22">{{ scope.row.quiz_content }}</el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">最大赢家: </el-col>
                                                <el-col :span="22">
                                                    <el-link type="primary" :underline="false"
                                                        @click="mainStore.gotoUserDetail(scope.row.big_winner_id)">{{
                                                            scope.row.big_winner
                                                        }}</el-link>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">竞猜选项: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="scope.row.quiz_options" style="width: 100%" border>
                                                        <el-table-column prop="option_id" label="选项ID" />
                                                        <el-table-column prop="option_name" label="选项描述" />
                                                        <el-table-column label="操作">
                                                            <template #default="props">
                                                                <el-space wrap>
                                                                    <el-link type="info"
                                                                        @click="editTournamentQuizOption(props.row)">编辑</el-link>
                                                                    <el-link
                                                                        :type="tournamentStore.tournamentQuizState[scope.row.quiz_state].type"
                                                                        @click="tournamentQuizSettlement(scope.row.quiz_id, props.row.option_id, props.row.option_name)">结算</el-link>
                                                                    <el-link type="danger"
                                                                        @click="deleteTournamentQuizOption(scope.row.quiz_id, props.row.option_id)">删除</el-link>
                                                                </el-space>
                                                            </template>
                                                        </el-table-column>
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2"></el-col>
                                                <el-col :span="22">
                                                    <el-button type="primary"
                                                        @click="addTournamentQuizOption(scope.row.quiz_id)"
                                                        size="large">增加选项
                                                    </el-button>
                                                </el-col>
                                            </el-row>
                                        </el-space>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                            </template>
                        </el-table-column>
                        <el-table-column prop="id" label="ID">
                            <template #default="scope">
                                <span style="color: #F56C6C;" v-if="scope.row.quiz_state === 1">{{ scope.row.quiz_id
                                }}</span>
                                <span style="color: #E6A23C;" v-else-if="scope.row.quiz_state === 0">{{ scope.row.quiz_id
                                }}</span>
                                <span v-else>{{ scope.row.quiz_id }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="stage" label="阶段" />
                        <el-table-column prop="quiz_title" label="标题" />
                        <el-table-column prop="start_time" label="开始时间" />
                        <el-table-column prop="end_time" label="结束时间" />
                        <el-table-column label="状态">
                            <template #default="scope">
                                <el-text class="mx-1" :type="tournamentStore.tournamentQuizState[scope.row.quiz_state].type"
                                    size="large">{{
                                        tournamentStore.tournamentQuizState[scope.row.quiz_state].label }}</el-text>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="info" @click.stop="editTournamentQuiz(scope.row)">编辑</el-link>
                                    <el-link type="danger"
                                        @click.stop="deleteTournamentQuiz(scope.row.quiz_id)">删除</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="addTournamentQuizDialog" title="添加竞猜">
        <el-form :model="addTournamentQuizForm" :rules="addTournamentQuizFormRules" ref="addTournamentQuizRuleFormRef"
            class="add-quizt-form" status-icon>
            <el-form-item label="赛事" prop="competition_id" required>
                <el-select v-model="addTournamentQuizForm.competition_id" placeholder="选择赛事" size="large">
                    <el-option v-for="item in tournament.list" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="标题" prop="title" required>
                <el-input placeholder="请输入标题(控制在20字内)" v-model="addTournamentQuizForm.title" type="text" maxlength="20"
                    size="large" />
            </el-form-item>
            <el-form-item label="内容" prop="content" required>
                <el-input type="textarea" v-model="addTournamentQuizForm.content" :rows="4"
                    placeholder="请输入竞猜内容(暂定控制在128字内)" maxlength="128" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="阶段">
                <el-input placeholder="赛事阶段(竞猜标题栏左侧标签,可选,控制在10字内)" v-model="addTournamentQuizForm.stage" type="text"
                    maxlength="10" size="large" />
            </el-form-item>
            <el-form-item label="时间" prop="time_range" required>
                <div class="block">
                    <el-date-picker v-model="addTournamentQuizForm.time_range" type="datetimerange" start-placeholder="开始时间"
                        end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" :unlink-panels="true" />
                </div>
            </el-form-item>
            <el-form-item :label="'选项' + item.id" v-for="(item, index) in addTournamentQuizForm.options" :key="item.id">
                <el-space wrap>
                    <el-input placeholder="请输入选项内容" v-model="item.option_name" type="text" maxlength="11" size="large" />
                    <el-button type="danger" @click="deleteTournamentQuizFormOption(item.id)" size="large"
                        v-if="item.id > 2">删除
                    </el-button>
                </el-space>
            </el-form-item>
            <el-form-item label="">
                <el-button type="primary" @click="addTournamentQuizFormOption()" size="large">增加选项
                </el-button>
            </el-form-item>
            <el-form-item label="展示" prop="is_show" required>
                <el-switch v-model="addTournamentQuizForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addTournamentQuizSubmit(addTournamentQuizRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="addTournamentQuizDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="editTournamentQuizDialog" title="编辑竞猜">
        <el-form :model="editTournamentQuizForm" :rules="editTournamentQuizFormRules" ref="editTournamentQuizRuleFormRef"
            class="add-quizt-form" status-icon>
            <el-form-item label="ID">
                <el-input placeholder="ID" v-model="editTournamentQuizForm.quiz_id" type="text" disabled />
            </el-form-item>
            <el-form-item label="赛事">
                <el-select v-model="editTournamentQuizForm.competition_id" placeholder="选择赛事" size="large" disabled>
                    <el-option v-for="item in tournament.list" :key="item.id" :label="item.title" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="标题" prop="title" required>
                <el-input placeholder="请输入标题(控制在20字内)" v-model="editTournamentQuizForm.title" type="text" maxlength="20"
                    size="large" />
            </el-form-item>
            <el-form-item label="内容" prop="content" required>
                <el-input type="textarea" v-model="editTournamentQuizForm.content" :rows="4"
                    placeholder="请输入竞猜内容(暂定控制在128字内)" maxlength="128" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="阶段">
                <el-input placeholder="赛事阶段(竞猜标题栏左侧标签,可选,控制在10字内)" v-model="editTournamentQuizForm.stage" type="text"
                    maxlength="10" size="large" />
            </el-form-item>
            <el-form-item label="时间" prop="time_range" required>
                <div class="block">
                    <el-date-picker v-model="editTournamentQuizForm.time_range" type="datetimerange"
                        start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss"
                        :unlink-panels="true" />
                </div>
            </el-form-item>
            <el-form-item label="展示" prop="is_show" required>
                <el-switch v-model="editTournamentQuizForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="editTournamentQuizSubmit(editTournamentQuizRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="editTournamentQuizDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="addTournamentQuizOptionDialog" title="添加竞猜选项">
        <el-form :model="addTournamentQuizOptionForm" :rules="addTournamentQuizOptionFormRules"
            ref="addTournamentQuizOptionRuleFormRef" class="add-option-form" status-icon>
            <el-form-item label="竞猜ID">
                <el-input placeholder="ID" v-model="addTournamentQuizOptionForm.quiz_id" type="text" disabled />
            </el-form-item>
            <el-form-item label="选项内容" prop="option_name" required>
                <el-input placeholder="请输入选项内容" v-model="addTournamentQuizOptionForm.option_name" type="text" maxlength="11"
                    size="large" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addTournamentQuizOptionSubmit(addTournamentQuizOptionRuleFormRef)"
                    size="large">确定
                </el-button>
                <el-button @click="addTournamentQuizOptionDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="editTournamentQuizOptionDialog" title="编辑竞猜选项">
        <el-form :model="editTournamentQuizOptionForm" :rules="editTournamentQuizOptionFormRules"
            ref="editTournamentQuizOptionRuleFormRef" class="edit-option-form" status-icon>
            <el-form-item label="竞猜ID">
                <el-input placeholder="ID" v-model="editTournamentQuizOptionForm.quiz_id" type="text" disabled />
            </el-form-item>
            <el-form-item label="选项ID">
                <el-input placeholder="ID" v-model="editTournamentQuizOptionForm.option_id" type="text" disabled />
            </el-form-item>
            <el-form-item label="选项内容" prop="option_name" required>
                <el-input placeholder="请输入选项内容" v-model="editTournamentQuizOptionForm.option_name" type="text"
                    maxlength="11" size="large" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="editTournamentQuizOptionSubmit(editTournamentQuizOptionRuleFormRef)"
                    size="large">确定
                </el-button>
                <el-button @click="editTournamentQuizOptionDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped></style>