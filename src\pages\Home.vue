<script setup lang="ts">
import { <PERSON>, Switch<PERSON><PERSON>on, User, <PERSON>u } from '@element-plus/icons-vue';
import { useMainStore } from '../store';
import { useRoute } from 'vue-router';
import { router } from '../router';

const route = useRoute();
const mainStore = useMainStore();

mainStore.readUserInfo();

//mainStore.getGameCompanyList();
//mainStore.fetchServerList();
//mainStore.getMenuList();

function menuGoRoute(name: string) {
    if (route.name === name) {
        return;
    }
    router.push({
        name: name
    });
}

</script>

<template>
    <div class="common-layout">
        <el-container class="admin-body">
            <el-header class="admin-header">
                <div class="header-left">
                    <!-- <img src="../assets/img/logo.png" alt="" /> -->
                    <h2 class="title">Global Match Management System</h2>
                </div>
                <div class="header-right">
                    <el-menu class="header-menu" :default-active='route.name' :ellipsis="false" mode="horizontal"
                        background-color="#373D41" text-color="#fff" active-text-color="#ffd04b">
                        <!-- <el-menu-item :index="item.path_name" @click="menuGoRoute(item.path_name)"
                            v-for="(item, index) in mainStore.getterFirstMenu" :key="item.menu_id">
                            <span>{{item.menu_name}}</span>
                        </el-menu-item> -->
                        <!-- <el-menu-item index="activity" @click="menuGoRoute('activity')">
                            <el-icon>
                                <User />
                            </el-icon>
                            <span>活动相关</span>
                        </el-menu-item>
                        <el-menu-item index="outsourcing" @click="menuGoRoute('outsourcing')">
                            <el-icon>
                                <User />
                            </el-icon>
                            <span>外协管理</span>
                        </el-menu-item> -->
                        <el-menu-item>Welcome, {{ mainStore.userInfo.name }}</el-menu-item>
                        <!-- <el-sub-menu index="2">
                            <span>Welcome, {{ mainStore.userInfo.name }}</span>
                            <template #title>Welcome，{{ mainStore.userInfo.name }}</template>
                            <el-menu-item index="2-1">{{ mainStore.userInfo.role }}</el-menu-item>
                            <el-menu-item index="2-2">修改密码</el-menu-item>
                        </el-sub-menu> -->
                        <el-menu-item @click="mainStore.logout()">
                            <span>退出</span>
                            <el-icon>
                                <SwitchButton />
                            </el-icon>
                        </el-menu-item>
                    </el-menu>

                </div>
            </el-header>
            <el-container class="main-container">
                <!-- <Suspense>
                    <router-view></router-view>
                </Suspense> -->
                <el-aside class="admin-aside">
                    <el-menu class="admin-menu" :default-active="route.name" text-color="#fff"
                        background-color="#333744">
                        <el-menu-item index="gameList" @click="menuGoRoute('gameList')">
                            <el-icon>
                                <Menu />
                            </el-icon>
                            <span>Mall Games</span>
                        </el-menu-item>
                    </el-menu>
                    <el-menu class="admin-menu" :default-active="route.name" text-color="#fff"
                        background-color="#333744">
                        <el-menu-item index="usersBuyGameRecord" @click="menuGoRoute('usersBuyGameRecord')">
                            <el-icon>
                                <Menu />
                            </el-icon>
                            <span>User Records</span>
                        </el-menu-item>
                    </el-menu>
                    <el-menu class="admin-menu" :default-active="route.name" text-color="#fff"
                        background-color="#333744">
                        <el-menu-item index="gameSalesData" @click="menuGoRoute('gameSalesData')">
                            <el-icon>
                                <Menu />
                            </el-icon>
                            <span>Sales Data</span>
                        </el-menu-item>
                    </el-menu>
                    <el-menu class="admin-menu" :default-active="route.name" text-color="#fff"
                        background-color="#333744">
                        <el-menu-item index="settlement" @click="menuGoRoute('settlement')">
                            <el-icon>
                                <Menu />
                            </el-icon>
                            <span>Settlement</span>
                        </el-menu-item>
                    </el-menu>
                    <el-menu class="admin-menu" :default-active="route.name" text-color="#fff"
                        background-color="#333744">
                        <el-menu-item index="versionManage" @click="menuGoRoute('versionManage')">
                            <el-icon>
                                <Menu />
                            </el-icon>
                            <span>版本管理</span>
                        </el-menu-item>
                    </el-menu>
                </el-aside>
                <el-main class="admin-content">
                    <router-view></router-view>
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>

<style lang="less" scoped>
.common-layout {
    height: 100%;

    .admin-body {
        height: 100%;
    }

    .admin-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #373D41;

        .header-left {
            display: flex;
            align-items: center;

            img {
                width: 50px;
                height: 50px;
            }

            .title {
                color: #fff;
                font-weight: 400;
                font-size: 18px;
            }
        }


    }
}
</style>
