<script setup lang="ts">
import { ArrowRight, House, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox, genFileId } from 'element-plus';
import { HomePageBannerItem } from '../../interface';

const mainStore = useMainStore();

const addBannerDialog = ref(false);
const editBannerDialog = ref(false);
const loading = ref(false);

const bannerData = reactive({
    data: [] as Array<HomePageBannerItem>
});

const addBannerBtnLock = ref(true);
const addBannerForm = reactive({
    title: '',
    game_series: 0,
    sav_game_id: 0,
    sav_ids: '',
    goto_type: 0,
    goto_url_1: '',
    goto_url_2: '',
    banner_url: '',
    banner_url_m: '',
    start_time: '',
    end_time: '',
    time_range: [] as Array<string>
});
const addBannerRuleFormRef = ref<FormInstance>();
const addBannerFormRules = reactive<FormRules>({
    goto_type: [
        {
            required: true, message: '请选择跳转类型', trigger: 'blur'
        }
    ],
    sav_game_id: [
        {
            required: true, message: '请选择游戏', trigger: 'blur'
        }
    ],
    sav_ids: [
        {
            required: true, message: '请输入录像ID', trigger: 'blur'
        }
    ],
    banner_url: [
        {
            required: true, message: '请上传PC版banner', trigger: 'blur'
        }
    ],
    banner_url_m: [
        {
            required: true, message: '请上传手机版banner', trigger: 'blur'
        }
    ],
    time_range: [
        {
            required: true, message: '请选择时间范围', trigger: 'blur'
        }
    ],
});

const editBannerBtnLock = ref(true);
const editBannerForm = reactive({
    id: 0,
    title: '',
    game_series: 0,
    sav_game_id: 0,
    sav_ids: '',
    goto_type: 0,
    goto_url_1: '',
    goto_url_2: '',
    banner_url: '',
    banner_url_m: '',
    start_time: '',
    end_time: '',
    time_range: [] as Array<string>
});
const editBannerRuleFormRef = ref<FormInstance>();
const editBannerFormRules = reactive<FormRules>({
    goto_type: [
        {
            required: true, message: '请选择跳转类型', trigger: 'blur'
        }
    ],
    banner_url: [
        {
            required: true, message: '请上传PC版banner', trigger: 'blur'
        }
    ],
    banner_url_m: [
        {
            required: true, message: '请上传手机版banner', trigger: 'blur'
        }
    ],
    time_range: [
        {
            required: true, message: '请选择时间范围', trigger: 'blur'
        }
    ],
});

const addUploadRef = ref<UploadInstance>();
const addPhoneUploadRef = ref<UploadInstance>();
const editUploadRef = ref<UploadInstance>();
const editPhoneUploadRef = ref<UploadInstance>();

const addGameList = computed(() => {
    return mainStore.gameList.filter(item => item.series_id === addBannerForm.game_series);
});
const editGameList = computed(() => {
    return mainStore.gameList.filter(item => item.series_id === editBannerForm.game_series);
});


getBanner();

async function getBanner() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getHomeBannerList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.bannerList) {
                    bannerData.data = res.data.bannerList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }

}

function addBanner() {
    addBannerDialog.value = true;
}

async function addBannerSubmit(formEl: FormInstance | undefined) {
    if (!addBannerBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (addBannerForm.title.length > 128) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (addBannerForm.goto_type === 4) {
                if (!addBannerForm.sav_ids) {
                    return mainStore.globalMessageTip('请输入录像ID', 3);
                }
            }
            if (!addBannerForm.banner_url) {
                return mainStore.globalMessageTip('请上传PC版图片', 3);
            }
            if (!addBannerForm.banner_url_m) {
                return mainStore.globalMessageTip('请上传手机版图片', 3);
            }
            if (addBannerForm.time_range.length !== 2) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            } else {
                addBannerForm.start_time = addBannerForm.time_range[0];
                addBannerForm.end_time = addBannerForm.time_range[1];
            }
            if (!addBannerForm.start_time || !addBannerForm.end_time) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${addBannerForm.goto_type}${addBannerForm.start_time}${addBannerForm.end_time}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            addBannerBtnLock.value = false;
            await requset.addHomeBanner({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                title: addBannerForm.title,
                savGameID: addBannerForm.sav_game_id,
                savIds: addBannerForm.sav_ids,
                iconUrl1: addBannerForm.banner_url,
                iconUrl2: addBannerForm.banner_url_m,
                gotoUrl1: addBannerForm.goto_url_1,
                gotoUrl2: addBannerForm.goto_url_2,
                gotoType: addBannerForm.goto_type,
                startTime: addBannerForm.start_time,
                endTime: addBannerForm.end_time,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('添加成功!', 0);
                    addBannerDialog.value = false;
                    addBannerForm.banner_url = '';
                    addBannerForm.banner_url_m = '';
                    addBannerForm.goto_url_1 = '';
                    addBannerForm.goto_url_2 = '';
                    addBannerForm.title = '';
                    addBannerForm.sav_ids = '';
                    addBannerForm.start_time = '';
                    addBannerForm.end_time = '';
                    addBannerForm.time_range = [];
                    addBannerForm.sav_game_id = 0;
                    addBannerForm.goto_type = 0;
                    addUploadRef.value?.clearFiles();
                    addPhoneUploadRef.value?.clearFiles();
                    getBanner();
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            addBannerBtnLock.value = true;
        } else {
            return;
        }
    });
}

function editBanner(item: HomePageBannerItem) {
    const gameSeriesIndex = mainStore.gameList.findIndex(item2 => item2.game_id === item.sav_game_id);
    if (gameSeriesIndex >= 0) {
        editBannerForm.game_series = mainStore.gameList[gameSeriesIndex].series_id;
    }
    editUploadRef.value?.clearFiles();
    editPhoneUploadRef.value?.clearFiles();
    editBannerForm.sav_game_id = item.sav_game_id;
    editBannerForm.id = item.id;
    editBannerForm.goto_type = item.goto_type;
    editBannerForm.banner_url = item.icon_url_1;
    editBannerForm.banner_url_m = item.icon_url_2;
    editBannerForm.sav_ids = item.sav_ids;
    editBannerForm.title = item.title;
    editBannerForm.goto_url_1 = item.goto_url_1;
    editBannerForm.goto_url_2 = item.goto_url_2;
    editBannerForm.time_range = [item.start_time, item.end_time];
    editBannerDialog.value = true;

}

async function deleteBanner(id: number) {
    if (!id) {
        return;
    }
    ElMessageBox.confirm(`确定要删除该海报?`, '删除海报', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.deleteHomeBanner({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetID: id,
            token: token
        }).then((res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('删除成功!', 0);
                getBanner();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该海报不存在!', 3);
                            getBanner();
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });

}


async function editBannerSubmit(formEl: FormInstance | undefined) {
    if (!editBannerBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (editBannerForm.title.length > 128) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (editBannerForm.goto_type === 4) {
                if (!editBannerForm.sav_ids) {
                    return mainStore.globalMessageTip('请输入录像ID', 3);
                }
            }
            if (!editBannerForm.banner_url) {
                return mainStore.globalMessageTip('请上传PC版图片', 3);
            }
            if (!editBannerForm.banner_url_m) {
                return mainStore.globalMessageTip('请上传手机版图片', 3);
            }
            if (editBannerForm.time_range.length !== 2) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            } else {
                editBannerForm.start_time = editBannerForm.time_range[0];
                editBannerForm.end_time = editBannerForm.time_range[1];
            }
            if (!editBannerForm.start_time || !editBannerForm.end_time) {
                return mainStore.globalMessageTip('请选择时间范围', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${editBannerForm.id}${editBannerForm.goto_type}${editBannerForm.start_time}${editBannerForm.end_time}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            editBannerBtnLock.value = false;
            await requset.editHomeBanner({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetID: editBannerForm.id,
                title: editBannerForm.title,
                savGameID: editBannerForm.sav_game_id,
                savIds: editBannerForm.sav_ids,
                iconUrl1: editBannerForm.banner_url,
                iconUrl2: editBannerForm.banner_url_m,
                gotoUrl1: editBannerForm.goto_url_1,
                gotoUrl2: editBannerForm.goto_url_2,
                gotoType: editBannerForm.goto_type,
                startTime: editBannerForm.start_time,
                endTime: editBannerForm.end_time,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('修改成功!', 0);
                    editBannerDialog.value = false;
                    editBannerForm.id = 0;
                    editBannerForm.banner_url = '';
                    editBannerForm.banner_url_m = '';
                    editBannerForm.goto_url_1 = '';
                    editBannerForm.goto_url_2 = '';
                    editBannerForm.title = '';
                    editBannerForm.sav_ids = '';
                    editBannerForm.start_time = '';
                    editBannerForm.end_time = '';
                    editBannerForm.time_range = [];
                    editBannerForm.sav_game_id = 0;
                    editBannerForm.goto_type = 0;
                    editUploadRef.value?.clearFiles();
                    editPhoneUploadRef.value?.clearFiles();
                    getBanner();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该海报不存在!', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            editBannerBtnLock.value = true;
        } else {
            return;
        }
    });
}

function bannerUploadBefore(file: any) {
    console.log(file);
    if (file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'image/jpeg') {
        mainStore.globalMessageTip('只能上传PNG/JPG类型图片', 3);
        return false;
    }
    if (file.size > 5120000) {
        mainStore.globalMessageTip('文件太大了', 3);
        return false;
    }
}

const addUploadExceed: UploadProps['onExceed'] = (files) => {
    addUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    addUploadRef.value!.handleStart(file);
    addUploadRef.value!.submit();
}
const addPhoneUploadExceed: UploadProps['onExceed'] = (files) => {
    addPhoneUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    addPhoneUploadRef.value!.handleStart(file);
    addPhoneUploadRef.value!.submit();
}
const editUploadExceed: UploadProps['onExceed'] = (files) => {
    editUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    editUploadRef.value!.handleStart(file);
    editUploadRef.value!.submit();
}
const editPhoneUploadExceed: UploadProps['onExceed'] = (files) => {
    editPhoneUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    editPhoneUploadRef.value!.handleStart(file);
    editPhoneUploadRef.value!.submit();
}


function bannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                addBannerForm.banner_url = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function phoneBannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                addBannerForm.banner_url_m = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function editBannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                editBannerForm.banner_url = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function editPhoneBannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                editBannerForm.banner_url_m = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function bannerUploadError(err: any) {
    mainStore.globalMessageTip(err, 3);
}

function addSelectGameSeries(val: number) {
    const gameIndex = mainStore.gameList.findIndex(item => item.series_id === val);
    if (gameIndex >= 0) {
        addBannerForm.sav_game_id = mainStore.gameList[gameIndex].game_id;
    }
}

function editSelectGameSeries(val: number) {
    const gameIndex = mainStore.gameList.findIndex(item => item.series_id === val);
    if (gameIndex >= 0) {
        editBannerForm.sav_game_id = mainStore.gameList[gameIndex].game_id;
    }
}

async function sortBanner(id: number, type: number) {
    if (!id) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${id}${type}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    mainStore.globalLoading(true);
    await requset.sortHomePageRecSav({
        userID: userID,
        identityToken: identityToken,
        authorityID: authorityID,
        targetID: id,
        sortType: type,
        token: token
    }).then((res) => {
        if (res.code === 0) {
            mainStore.globalMessageTip('修改成功!', 0);
            getBanner();
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                switch (res.code) {
                    case 5:
                    case 6:
                        mainStore.globalMessageTip('记录已过时, 正在刷新...', 3);
                        getBanner();
                        break;
                    default:
                        break;
                }
            }
        }
    }).catch((err) => {
        mainStore.dealResponseErrInfo(-1);
    });
    mainStore.globalLoading(false);
    editBannerBtnLock.value = true;
}


</script>

<template>
    <div class="banner-config" v-if="mainStore.checkPermission(2003)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'inside'
                }">
                    约战内部
                </el-breadcrumb-item>
                <el-breadcrumb-item>平台海报管理</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-button type="primary" size="large" @click="addBanner()">添加海报
                                </el-button>
                                <el-button type="primary" size="large" @click="getBanner()">刷新
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table v-loading="loading" :data="bannerData.data" border style="width: 100%">
                        <el-table-column prop="id" label="ID">
                            <template #default="scope">
                                <span style="color: #67C23A;" v-if="scope.row.state === 1">{{ scope.row.id }}</span>
                                <span v-else>{{ scope.row.id }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="title" label="标题" />
                        <el-table-column label="跳转类型">
                            <template #default="scope">
                                <span>{{ mainStore.getBannerGotoTypeName(scope.row.goto_type) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="PC图片">
                            <template #default="scope">
                                <el-image fit="contain" :src="scope.row.icon_url_1">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                            </template>
                        </el-table-column>
                        <el-table-column label="手机图片">
                            <template #default="scope">
                                <el-image fit="contain" :src="scope.row.icon_url_2">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                            </template>
                        </el-table-column>
                        <el-table-column prop="goto_url_1" label="PC链接" />
                        <el-table-column prop="goto_url_2" label="手机链接" />
                        <el-table-column prop="start_time" label="开始时间" />
                        <el-table-column prop="end_time" label="结束时间" />
                        <el-table-column label="移动">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="primary"
                                        :disabled="scope.row.id === bannerData.data[0].id ? true : false"
                                        @click="sortBanner(scope.row.id, 1)">置顶</el-link>
                                    <el-link type="success"
                                        :disabled="scope.row.id === bannerData.data[0].id ? true : false"
                                        @click="sortBanner(scope.row.id, 2)">上移</el-link>
                                    <el-link type="warning"
                                        :disabled="scope.row.id === bannerData.data[bannerData.data.length - 1].id ? true : false"
                                        @click="sortBanner(scope.row.id, 3)">
                                        下移</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="info" @click="editBanner(scope.row)">编辑</el-link>
                                    <el-link type="danger" @click="deleteBanner(scope.row.id)">删除</el-link>
                                </el-space>

                                <!-- <el-button type="primary" @click="editBanner(scope.row)">编辑
                                </el-button>
                                <el-button type="danger" @click="deleteBanner(scope.row.id)">删除
                                </el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                </el-space>


            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="addBannerDialog" title="添加海报">
        <el-form :model="addBannerForm" :rules="addBannerFormRules" ref="addBannerRuleFormRef" class="add-banner-form"
            status-icon>
            <el-form-item label="标题">
                <el-input placeholder="请输入标题(可选)" v-model="addBannerForm.title" type="text" maxlength="128"
                    size="large" />
            </el-form-item>
            <el-form-item label="跳转类型" prop="goto_type" required>
                <el-select v-model="addBannerForm.goto_type" placeholder="选择跳转类型" size="large">
                    <el-option v-for="item in mainStore.bannerGotoType" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="游戏" prop="sav_game_id" required v-if="addBannerForm.goto_type === 4">
                <el-space wrap>
                    <el-select v-model="addBannerForm.game_series" placeholder="请选择游戏系列" size="large"
                        @change="addSelectGameSeries">
                        <el-option v-for="(item, index) in mainStore.gameSeries" :key="item.series_id"
                            :label="item.series_name" :value="item.series_id" />
                    </el-select>
                    <el-select v-model="addBannerForm.sav_game_id" placeholder="请选择游戏" size="large">
                        <el-option v-for="item in addGameList" :key="item.game_id" :label="item.game_name"
                            :value="item.game_id" />
                    </el-select>
                </el-space>
            </el-form-item>
            <el-form-item label="录像ID" prop="sav_ids" required v-if="addBannerForm.goto_type === 4">
                <el-input type="textarea" v-model="addBannerForm.sav_ids" :rows="4" placeholder="请输入录像ID" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="PC链接">
                <el-input placeholder="请输入PC跳转链接(可选)" v-model="addBannerForm.goto_url_1" type="text" maxlength="256"
                    size="large" />
            </el-form-item>
            <el-form-item label="手机链接">
                <el-input placeholder="请输入手机跳转链接(可选)" v-model="addBannerForm.goto_url_2" type="text" maxlength="256"
                    size="large" />
            </el-form-item>
            <el-form-item label="时间范围" prop="time_range" required>
                <div class="block">
                    <el-date-picker v-model="addBannerForm.time_range" type="datetimerange" start-placeholder="开始时间"
                        end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" :unlink-panels="true" />
                </div>
            </el-form-item>
            <el-form-item label="PC图片" required>
                <el-upload class="upload-demo" drag accept=".jpeg, .png, .jpg" :limit="1" :on-exceed="addUploadExceed"
                    ref="addUploadRef" :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :before-upload="bannerUploadBefore" :on-success="bannerUploadSuccess"
                    :on-error="bannerUploadError" size="large">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%">
                    <el-image fit="contain" :src="addBannerForm.banner_url">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="手机图片" required>
                <el-upload class="upload-demo" ref="addPhoneUploadRef" drag accept=".jpeg, .png, .jpg" :limit="1"
                    :on-exceed="addPhoneUploadExceed" :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :before-upload="bannerUploadBefore" :on-success="phoneBannerUploadSuccess"
                    :on-error="bannerUploadError">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%">
                    <el-image fit="contain" :src="addBannerForm.banner_url_m">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addBannerSubmit(addBannerRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="addBannerDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>

    <el-dialog v-model="editBannerDialog" title="编辑海报">
        <el-form :model="editBannerForm" :rules="editBannerFormRules" ref="editBannerRuleFormRef"
            class="edit-banner-form" status-icon>
            <el-form-item label="ID" prop="id" required>
                <el-input placeholder="ID" v-model="editBannerForm.id" type="text" disabled />
            </el-form-item>
            <el-form-item label="标题">
                <el-input placeholder="请输入标题(可选)" v-model="editBannerForm.title" type="text" maxlength="128" />
            </el-form-item>
            <el-form-item label="跳转类型" prop="goto_type" required>
                <el-select v-model="editBannerForm.goto_type" placeholder="选择跳转类型" size="large">
                    <el-option v-for="item in mainStore.bannerGotoType" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="游戏" prop="sav_game_id" required v-if="editBannerForm.goto_type === 4">
                <el-space wrap>
                    <el-select v-model="editBannerForm.game_series" placeholder="请选择游戏系列" size="large"
                        @change="editSelectGameSeries">
                        <el-option v-for="(item, index) in mainStore.gameSeries" :key="item.series_id"
                            :label="item.series_name" :value="item.series_id" />
                    </el-select>
                    <el-select v-model="editBannerForm.sav_game_id" placeholder="请选择游戏" size="large">
                        <el-option v-for="item in editGameList" :key="item.game_id" :label="item.game_name"
                            :value="item.game_id" />
                    </el-select>
                </el-space>
            </el-form-item>
            <el-form-item label="录像ID" prop="sav_ids" required v-if="editBannerForm.goto_type === 4">
                <el-input type="textarea" v-model="editBannerForm.sav_ids" placeholder="请输入录像ID"></el-input>
            </el-form-item>
            <el-form-item label="PC链接">
                <el-input placeholder="请输入PC跳转链接(可选)" v-model="editBannerForm.goto_url_1" type="text" maxlength="256"
                    size="large" />
            </el-form-item>
            <el-form-item label="手机链接">
                <el-input placeholder="请输入手机跳转链接(可选)" v-model="editBannerForm.goto_url_2" type="text" maxlength="256"
                    size="large" />
            </el-form-item>
            <el-form-item label="时间范围" prop="time_range" required>
                <div class="block">
                    <el-date-picker v-model="editBannerForm.time_range" type="datetimerange" start-placeholder="开始时间"
                        end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" :unlink-panels="true" />
                </div>
            </el-form-item>
            <el-form-item label="PC图片" required>
                <el-upload class="upload-demo" ref="editUploadRef" drag accept=".jpeg, .png, .jpg"
                    :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :limit="1" :on-exceed="editUploadExceed" :before-upload="bannerUploadBefore"
                    :on-success="editBannerUploadSuccess" :on-error="bannerUploadError">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%;">
                    <el-image fit="contain" :src="editBannerForm.banner_url">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="手机图片" required>
                <el-upload class="upload-demo" ref="editPhoneUploadRef" drag accept=".jpeg, .png, .jpg"
                    :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :limit="1" :on-exceed="editPhoneUploadExceed" :before-upload="bannerUploadBefore"
                    :on-success="editPhoneBannerUploadSuccess" :on-error="bannerUploadError">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block">
                    <el-image fit="contain" :src="editBannerForm.banner_url_m">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="editBannerSubmit(editBannerRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="editBannerDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped>

</style>