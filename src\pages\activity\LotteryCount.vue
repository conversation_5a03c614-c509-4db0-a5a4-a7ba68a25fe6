<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useActivityStore } from '../../store/activity';
import { reactive, ref } from 'vue';
import { NicknameReview } from '../../interface';

const mainStore = useMainStore();
const activityStore = useActivityStore();

const loading = ref(false);

const currentPage = ref(1);

const filterForm = reactive({

});
const filterFormRules = [
    {

    }
];


getLotteryRecord();

async function getLotteryRecord() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${currentPage.value}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getUsersLotteryRecord({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            currentPage: currentPage.value,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    activityStore.lotteryTotal = res.data.total;
                    activityStore.lotteryUserNum = res.data.user_num;
                    if (res.data.lotteryRecord) {
                        activityStore.lotteryRecord = res.data.lotteryRecord;
                    }
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function currentPageChange(page: any) {
    currentPage.value = page;
    await getLotteryRecord();
}

</script>

<template>
    <div class="nickname-review" v-if="mainStore.checkPermission(5002)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'activity'
                }">
                    活动管理
                </el-breadcrumb-item>
                <el-breadcrumb-item>夺宝统计</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-button type="primary" @click="getLotteryRecord()">刷新列表
                                </el-button>
                                <span>夺宝次数：{{ activityStore.lotteryTotal }}</span>
                                <span>夺宝人数：{{ activityStore.lotteryUserNum }}</span>
                            </el-space>
                        </el-col>
                    </el-row>
                    <!-- <el-form ref="filterFormRef" :model="filterForm" status-icon :rules="filterFormRules"
                        label-width="120px" class="filter-form">
                        <el-form-item label="举报游戏">
                            <el-space wrap>
                                <el-select-v2 v-model="filterForm.game_series" clearable :options="gameSeriesOptions"
                                    placeholder="选择游戏分类" @change="selectGameSeries" @clear="clearGameSeries" />
                                <el-select-v2 v-model="filterForm.game_id" clearable filterable multiple collapse-tags
                                    collapse-tags-tooltip :options="gameListOptions" style="width: 240px"
                                    placeholder="选择游戏名称" @change="selectGame" />
                            </el-space>
                        </el-form-item>
                        <el-form-item label="举报类型">
                            <el-radio-group v-model="filterForm.report_type">
                                <el-radio :label="item.type_id" v-for="(item, index) in reportTypeList"
                                    :key="item.type_id">{{ item.type_name }}</el-radio>
                            </el-radio-group>

                        </el-form-item>
                        <el-form-item label="">
                            <el-button type="primary" @click="getReportTask()">获取任务
                            </el-button>
                        </el-form-item>
                    </el-form> -->
                    <el-table v-loading="loading" :data="activityStore.lotteryRecord" border style="width: 100%">
                        <el-table-column prop="id" label="序号" />
                        <el-table-column prop="user_id" label="用户ID" />
                        <el-table-column prop="nickname" label="昵称" />
                        <el-table-column prop="lottery_name" label="夺宝类型" />
                        <el-table-column prop="reward_name" label="奖励名称" />
                        <el-table-column prop="lottery_time" label="夺宝时间" />
                    </el-table>
                    <el-pagination background layout="prev, pager, next" :total="activityStore.lotteryTotal"
                        :current-page="currentPage" @current-change="currentPageChange" />
                </el-space>
            </el-card>
        </el-space>


    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped>
</style>