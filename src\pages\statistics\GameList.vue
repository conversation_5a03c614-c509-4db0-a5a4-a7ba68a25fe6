<script setup lang="ts">
import { ArrowRight, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { computed, reactive, ref } from 'vue';
import { FormInstance, FormRules, genFileId, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox } from 'element-plus';
import Big from 'big.js';

const mainStore = useMainStore();

const loading = ref(false);
const settlementLock = ref(false);

const filterForm = reactive({
    company_id: mainStore.gameCompanyList[0]?.company_id || '',
});

const currentPage = ref(1);

fetchCompanyMallGame();

// getMallGameList();


function selectCompany(val: number) {
    mainStore.getGameList();
}

async function getMallGameList() {
    if (filterForm.company_id) {
        loading.value = true;
        await mainStore.getMallGameWithCompany(Number(filterForm.company_id));
        loading.value = false;
    }
}

async function fetchCompanyMallGame() {
    mainStore.globalLoading(true);
    await mainStore.getGameCompanyList();
    if (mainStore.gameCompanyList[0]) {
        filterForm.company_id = mainStore.gameCompanyList[0].company_id;
    }
    if (filterForm.company_id) {
        await mainStore.getMallGameWithCompany(Number(filterForm.company_id));
    }
    mainStore.globalLoading(false);
}

async function settlementAllGame() {
    if (settlementLock.value) {
        return mainStore.globalMessageTip('Settlement, please wait...', 1);
    }
    if (!filterForm.company_id || Number(filterForm.company_id) < 0) {
        return mainStore.globalMessageTip('For information abnormal, please refresh the page', 3);
    }
    ElMessageBox.confirm(`Are you sure to settle all games of ${mainStore.getterCurrentCompany(Number(filterForm.company_id))?.company_name}?`, 'Settlement income', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning',
        center: true,
    }).then(async () => {
        settlementLock.value = true;
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${filterForm.company_id}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            let req_success = false;
            await requset.settlementAllCompanyGame({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                companyID: Number(filterForm.company_id),
                token: token
            }).then(res => {
                if (res.code === 0) {
                    req_success = true;
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip("The settleable amount is 0", 2);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            if (req_success) {
                await mainStore.getMallGameWithCompany(Number(filterForm.company_id));
            }
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
        settlementLock.value = false;
    }).catch(() => {

    });
}


function currentPageChange(page: number) {
    currentPage.value = page;
    getMallGameList();
}

</script>

<template>
    <div class="banner-config">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item>
                    Manage
                </el-breadcrumb-item>
                <el-breadcrumb-item>Mall game list</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>Game company: </span>
                                <el-select v-model="filterForm.company_id" placeholder="Select company" size="large"
                                    @change="selectCompany">
                                    <el-option v-for="(item, index) in mainStore.gameCompanyList" :key="item.company_id"
                                        :label="item.company_name" :value="item.company_id" />
                                </el-select>

                                <el-button type="primary" size="large" @click="getMallGameList">Refresh
                                </el-button>

                            </el-space>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-space wrap>
                            <span>Total clearable income: <b>${{
                mainStore.getterCurrentCompany(Number(filterForm.company_id))?.all_unsettled_revenue
            }}</b></span>
                            <el-button type="danger" size="large" @click="settlementAllGame">Settlement All
                            </el-button>
                            <span>Last update: <b>{{ mainStore.lastStatisticsTime }}</b></span>
                            <span>(Hourly Statistics)</span>
                        </el-space>
                    </el-row>
                    <el-table 
                        :data="mainStore.getterCurrentCompany(Number(filterForm.company_id))?.mall_games" border
                        style="width: 100%">
                        <!-- <el-table-column prop="game_id" label="ID" /> -->
                        <el-table-column prop="game_name" label="Game name" />
                        <el-table-column label="Game icon">
                            <template #default="scope">
                                <el-image style="width: 100px; height: 100px" :src="scope.row.game_main_icon" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="launch_time" label="Launch time" />
                        <el-table-column label="Game price">
                            <template #default="scope">
                                ${{ new Big(scope.row.price).div(100) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="sales_quantity" label="Sales quantity" />
                        <el-table-column prop="ratio" label="Ratio" />
                        <el-table-column label="Total revenue">
                            <template #default="scope">
                                ${{ scope.row.total_revenue }}
                            </template>
                        </el-table-column>
                        <el-table-column label="Unsettled revenue">
                            <template #default="scope">
                                ${{ scope.row.unsettled_revenue }}
                            </template>
                        </el-table-column>
                        <el-table-column label="View">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="primary"
                                        @click="mainStore.gotoGameSalesDetail(scope.row.game_id)">Sales
                                        Details</el-link>
                                    <el-link type="success"
                                        @click="mainStore.gotoUsersBuyGameRecord(scope.row.game_id)">User List</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- <el-pagination background layout="prev, pager, next" :total="mainStore.getterCurrentCompany(Number(filterForm.company_id))?.mall_games?.length" :current-page="currentPage"
                        :page-size="15" @current-change="currentPageChange" /> -->
                </el-space>
            </el-card>
        </el-space>
    </div>
</template>

<style lang="less" scoped></style>