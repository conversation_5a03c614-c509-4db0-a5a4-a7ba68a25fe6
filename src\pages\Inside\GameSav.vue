<script setup lang="ts">
import { ArrowRight, House, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules, UploadInstance } from 'element-plus';
import { ElMessageBox } from 'element-plus';
import { GamePageRecSavItem, HomePageRecSavItem } from '../../interface';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const addGameSavDialog = ref(false);
const editGameSavDialog = ref(false);
const loading = ref(false);


const gameSavData = reactive({
    data: [] as Array<GamePageRecSavItem>
});

const filterForm = reactive({
    game_series: 0,
    game_id: 0,
});

const addGameSavBtnLock = ref(true);
const addGameSavForm = reactive({
    p1_id: '',
    p2_id: '',
    p1_win_num: '',
    p2_win_num: '',
    sav_ids: '',
    currency: 1,
    price: 0,
    origin_price: 0,
    is_show: 1,
});
const addGameSavRuleFormRef = ref<FormInstance>();
const addGameSavFormRules = reactive<FormRules>({
    p1_id: [
        {
            required: true, message: '请输入P1用户ID', trigger: 'blur'
        }
    ],
    p2_id: [
        {
            required: true, message: '请输入P2用户ID', trigger: 'blur'
        }
    ],
    p1_win_num: [
        {
            required: true, message: '请输入P1比分', trigger: 'blur'
        }
    ],
    p2_win_num: [
        {
            required: true, message: '请输入P2比分', trigger: 'blur'
        }
    ],
    sav_ids: [
        {
            required: true, message: '请输入录像ID', trigger: 'blur'
        }
    ],
    currency: [
        {
            required: true, message: '请选择货币类型', trigger: 'blur'
        }
    ],
    price: [
        {
            required: true, message: '请输入价格', trigger: 'blur'
        }
    ],
    origin_price: [
        {
            required: true, message: '请输入原价', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
});

const editGameSavBtnLock = ref(true);
const editGameSavForm = reactive({
    id: 0,
    p1_id: '' as number | '',
    p2_id: '' as number | '',
    p1_win_num: '' as number | '',
    p2_win_num: '' as number | '',
    sav_ids: '',
    currency: 1,
    price: 0,
    origin_price: 0,
    is_show: 1,
});
const editGameSavRuleFormRef = ref<FormInstance>();
const editGameSavFormRules = reactive<FormRules>({
    id: [
        {
            required: true, message: '请输入记录ID', trigger: 'blur'
        }
    ],
    p1_id: [
        {
            required: true, message: '请输入P1用户ID', trigger: 'blur'
        }
    ],
    p2_id: [
        {
            required: true, message: '请输入P2用户ID', trigger: 'blur'
        }
    ],
    p1_win_num: [
        {
            required: true, message: '请输入P1比分', trigger: 'blur'
        }
    ],
    p2_win_num: [
        {
            required: true, message: '请输入P2比分', trigger: 'blur'
        }
    ],
    sav_ids: [
        {
            required: true, message: '请输入录像ID', trigger: 'blur'
        }
    ],
    currency: [
        {
            required: true, message: '请选择货币类型', trigger: 'blur'
        }
    ],
    price: [
        {
            required: true, message: '请输入价格', trigger: 'blur'
        }
    ],
    origin_price: [
        {
            required: true, message: '请输入原价', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
});

const gameList = computed(() => {
    return mainStore.gameList.filter(item => item.series_id === filterForm.game_series);
});


getGamePageRecSav();

async function getGamePageRecSav() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${filterForm.game_id}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getGamePageRecSav({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
            gameID: filterForm.game_id,
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.gameSavList) {
                    gameSavData.data = res.data.gameSavList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }

}

function addGamePageSav() {
    addGameSavDialog.value = true;
}

async function addGamePageSavSubmit(formEl: FormInstance | undefined) {
    if (!addGameSavBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {

            const p1_id = parseInt(addGameSavForm.p1_id);
            const p2_id = parseInt(addGameSavForm.p2_id);
            const p1_win_num = parseInt(addGameSavForm.p1_win_num);
            const p2_win_num = parseInt(addGameSavForm.p2_win_num);

            if (filterForm.game_id < 0) {
                return mainStore.globalMessageTip('请选择游戏', 3);
            }
            if (!p1_id || p1_id < 0) {
                return mainStore.globalMessageTip('请输入正确的P1用户ID', 3);
            }
            if (!p2_id || p2_id < 0) {
                return mainStore.globalMessageTip('请输入正确的P2用户ID', 3);
            }
            if (p1_win_num < 0) {
                return mainStore.globalMessageTip('请输入正确的P1比分', 3);
            }
            if (p2_win_num < 0) {
                return mainStore.globalMessageTip('请输入正确的P2比分', 3);
            }
            if (!addGameSavForm.sav_ids) {
                return mainStore.globalMessageTip('请输入录像ID', 3);
            }
            if (addGameSavForm.price < 0 || addGameSavForm.origin_price < 0) {
                return mainStore.globalMessageTip('价格必须大于等于0', 3);
            }
            if (addGameSavForm.price > addGameSavForm.origin_price) {
                return mainStore.globalMessageTip('价格必须小于等于原价', 3);
            }
            if (!mainStore.recSavCurrency.some(item => item.id === addGameSavForm.currency)) {
                return mainStore.globalMessageTip('非法的货币类型', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${filterForm.game_id}${p1_id}${p2_id}${p1_win_num}${p2_win_num}${addGameSavForm.sav_ids}${addGameSavForm.currency}${addGameSavForm.price}${addGameSavForm.origin_price}${addGameSavForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            addGameSavBtnLock.value = false;
            await requset.addGamePageRecSav({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                p1ID: p1_id,
                p2ID: p2_id,
                p1WinNum: p1_win_num,
                p2WinNum: p2_win_num,
                gameID: filterForm.game_id,
                savIds: addGameSavForm.sav_ids,
                currency: addGameSavForm.currency,
                price: addGameSavForm.price,
                origin_price: addGameSavForm.origin_price,
                is_show: addGameSavForm.is_show,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('添加成功!', 0);
                    addGameSavDialog.value = false;
                    addGameSavForm.p1_id = '';
                    addGameSavForm.p2_id = '';
                    addGameSavForm.p1_win_num = '';
                    addGameSavForm.p2_win_num = '';
                    addGameSavForm.sav_ids = '';
                    addGameSavForm.currency = 1;
                    addGameSavForm.price = 0;
                    addGameSavForm.origin_price = 0;
                    addGameSavForm.is_show = 1;
                    getGamePageRecSav();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('P1用户不存在, 请检查ID是否正确!', 3);
                                break;
                            case 6:
                                mainStore.globalMessageTip('P2用户不存在, 请检查ID是否正确!', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            addGameSavBtnLock.value = true;
        } else {
            return;
        }
    });
}

function editHomePageSav(item: GamePageRecSavItem) {
    editGameSavForm.id = item.id;
    editGameSavForm.p1_id = item.p1_id;
    editGameSavForm.p2_id = item.p2_id;
    editGameSavForm.p1_win_num = item.p1_win_num;
    editGameSavForm.p2_win_num = item.p2_win_num;
    editGameSavForm.sav_ids = item.sav_ids;
    editGameSavForm.currency = item.currency;
    editGameSavForm.price = item.price;
    editGameSavForm.origin_price = item.origin_price;
    editGameSavForm.is_show = item.is_show;
    editGameSavDialog.value = true;

}

async function deleteHomePageSav(id: number) {
    if (!id) {
        return;
    }
    ElMessageBox.confirm(`确定要删除该推荐录像?`, '删除推荐录像', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.deleteGamePageRecSav({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetID: id,
            token: token
        }).then((res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('删除成功!', 0);
                getGamePageRecSav();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该推荐录像不存在!', 3);
                            getGamePageRecSav();
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });

}


async function editGamePageSavSubmit(formEl: FormInstance | undefined) {
    if (!editGameSavBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            const p1_id = parseInt(String(editGameSavForm.p1_id));
            const p2_id = parseInt(String(editGameSavForm.p2_id));
            const p1_win_num = parseInt(String(editGameSavForm.p1_win_num));
            const p2_win_num = parseInt(String(editGameSavForm.p2_win_num));

            if (!p1_id || p1_id < 0) {
                return mainStore.globalMessageTip('请输入正确的P1用户ID', 3);
            }
            if (!p2_id || p2_id < 0) {
                return mainStore.globalMessageTip('请输入正确的P2用户ID', 3);
            }
            if (p1_win_num < 0) {
                return mainStore.globalMessageTip('请输入正确的P1比分', 3);
            }
            if (p2_win_num < 0) {
                return mainStore.globalMessageTip('请输入正确的P2比分', 3);
            }
            if (!editGameSavForm.sav_ids) {
                return mainStore.globalMessageTip('请输入录像ID', 3);
            }
            if (editGameSavForm.price < 0 || editGameSavForm.origin_price < 0) {
                return mainStore.globalMessageTip('价格必须大于等于0', 3);
            }
            if (editGameSavForm.price > editGameSavForm.origin_price) {
                return mainStore.globalMessageTip('价格必须小于等于原价', 3);
            }
            if (!mainStore.recSavCurrency.some(item => item.id === editGameSavForm.currency)) {
                return mainStore.globalMessageTip('非法的货币类型', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${editGameSavForm.p1_id}${editGameSavForm.p2_id}${editGameSavForm.p1_win_num}${editGameSavForm.p2_win_num}${editGameSavForm.sav_ids}${editGameSavForm.currency}${editGameSavForm.price}${editGameSavForm.origin_price}${editGameSavForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            editGameSavBtnLock.value = false;
            await requset.editGamePageRecSav({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetID: editGameSavForm.id,
                p1ID: p1_id,
                p2ID: p2_id,
                p1WinNum: p1_win_num,
                p2WinNum: p2_win_num,
                savIds: editGameSavForm.sav_ids,
                currency: editGameSavForm.currency,
                price: editGameSavForm.price,
                origin_price: editGameSavForm.origin_price,
                is_show: editGameSavForm.is_show,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('修改成功!', 0);
                    editGameSavDialog.value = false;
                    getGamePageRecSav();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('P1用户不存在, 请检查ID是否正确!', 3);
                                break;
                            case 6:
                                mainStore.globalMessageTip('P2用户不存在, 请检查ID是否正确!', 3);
                                break;
                            case 7:
                                mainStore.globalMessageTip('该推荐录像不存在!', 3);
                                getGamePageRecSav();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            editGameSavBtnLock.value = true;
        } else {
            return;
        }
    });
}

function selectGameSeries(val: number) {
    const gameIndex = mainStore.gameList.findIndex(item => item.series_id === val);
    if (gameIndex >= 0) {
        filterForm.game_id = mainStore.gameList[gameIndex].game_id;
        getGamePageRecSav();
    }
}

function selectGame(val: number) {
    getGamePageRecSav();
}

async function sortHomePageRecSav(id: number, type: number) {
    if (!id) {
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${id}${type}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    mainStore.globalLoading(true);
    await requset.sortGamePageRecSav({
        userID: userID,
        identityToken: identityToken,
        authorityID: authorityID,
        targetID: id,
        sortType: type,
        token: token
    }).then((res) => {
        if (res.code === 0) {
            mainStore.globalMessageTip('修改成功!', 0);
            getGamePageRecSav();
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                switch (res.code) {
                    case 5:
                    case 6:
                        mainStore.globalMessageTip('记录已过时, 正在刷新...', 3);
                        getGamePageRecSav();
                        break;
                    default:
                        break;
                }
            }
        }
    }).catch((err) => {
        mainStore.dealResponseErrInfo(-1);
    });
    mainStore.globalLoading(false);
    editGameSavBtnLock.value = true;
}


</script>

<template>
    <div class="banner-config" v-if="mainStore.checkPermission(2004)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'inside'
                }">
                    约战内部
                </el-breadcrumb-item>
                <el-breadcrumb-item>游戏推荐录像</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-select v-model="filterForm.game_series" placeholder="请选择游戏系列" size="large"
                                    @change="selectGameSeries">
                                    <el-option v-for="(item, index) in mainStore.gameSeries" :key="item.series_id"
                                        :label="item.series_name" :value="item.series_id" />
                                </el-select>
                                <el-select v-model="filterForm.game_id" placeholder="请选择游戏" size="large"
                                    @change="selectGame">
                                    <el-option v-for="item in gameList" :key="item.game_id" :label="item.game_name"
                                        :value="item.game_id" />
                                </el-select>
                                <el-button type="primary" size="large" @click="addGamePageSav()">添加推荐录像
                                </el-button>
                                <el-button type="primary" size="large" @click="getGamePageRecSav()">刷新
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table v-loading="loading" :data="gameSavData.data" border style="width: 100%">
                        <el-table-column prop="id" label="ID" />
                        <el-table-column label="游戏">
                            <template #default="scope">
                                <span>{{ mainStore.getGameName(scope.row.game_id) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="P1_ID">
                            <template #default="scope">
                                <el-link type="primary" :underline="false"
                                    @click="mainStore.gotoUserDetail(scope.row.p1_id)">{{ scope.row.p1_name }}</el-link>
                            </template>
                        </el-table-column>
                        <el-table-column label="P2_ID">
                            <template #default="scope">
                                <el-link type="primary" :underline="false"
                                    @click="mainStore.gotoUserDetail(scope.row.p2_id)">{{ scope.row.p2_name }}</el-link>
                            </template>
                        </el-table-column>
                        <el-table-column prop="p1_win_num" label="P1比分" />
                        <el-table-column prop="p2_win_num" label="P2比分" />
                        <!-- <el-table-column prop="sav_ids" label="录像ID" /> -->
                        <el-table-column prop="price" label="价格" />
                        <el-table-column prop="origin_price" label="原价" />
                        <el-table-column prop="update_time" label="更新时间" />
                        <el-table-column label="移动">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="primary"
                                        :disabled="scope.row.id === gameSavData.data[0].id ? true : false"
                                        @click="sortHomePageRecSav(scope.row.id, 1)">置顶</el-link>
                                    <el-link type="danger"
                                        :disabled="scope.row.id === gameSavData.data[0].id ? true : false"
                                        @click="sortHomePageRecSav(scope.row.id, 2)">上移</el-link>
                                    <el-link type="warning"
                                        :disabled="scope.row.id === gameSavData.data[gameSavData.data.length - 1].id ? true : false"
                                        @click="sortHomePageRecSav(scope.row.id, 3)">
                                        下移</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="primary" @click="editHomePageSav(scope.row)">编辑</el-link>
                                    <el-link type="info" @click="deleteHomePageSav(scope.row.id)">删除</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="addGameSavDialog" title="添加推荐录像">
        <el-form :model="addGameSavForm" :rules="addGameSavFormRules" ref="addGameSavRuleFormRef" class="add-banner-form"
            status-icon>
            <el-form-item label="P1_ID" prop="p1_id" required>
                <el-input placeholder="请输入P1用户ID" v-model="addGameSavForm.p1_id" type="text" maxlength="30" size="large" />
            </el-form-item>
            <el-form-item label="P2_ID" prop="p2_id" required>
                <el-input placeholder="请输入P2用户ID" v-model="addGameSavForm.p2_id" type="text" maxlength="30" size="large" />
            </el-form-item>
            <el-form-item label="P1比分" prop="p1_win_num" required>
                <el-input placeholder="请输入P1比分" v-model="addGameSavForm.p1_win_num" type="text" maxlength="30"
                    size="large" />
            </el-form-item>
            <el-form-item label="P2比分" prop="p2_win_num" required>
                <el-input placeholder="请输入P2比分" v-model="addGameSavForm.p2_win_num" type="text" maxlength="30"
                    size="large" />
            </el-form-item>
            <el-form-item label="录像ID" prop="sav_ids" required>
                <el-input type="textarea" v-model="addGameSavForm.sav_ids" :rows="4" placeholder="请输入录像ID" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="货币" prop="currency" required>
                <el-select v-model="addGameSavForm.currency" placeholder="请选择货币" size="large" disabled>
                    <el-option v-for="(item, index) in mainStore.recSavCurrency" :key="item.id" :label="item.name"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="价格" prop="price" required>
                <el-input v-model="addGameSavForm.price" :rows="4" placeholder="请输入价格" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="原价" prop="origin_price" required>
                <el-input v-model="addGameSavForm.origin_price" :rows="4" placeholder="请输入原价" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="展示">
                <el-switch v-model="addGameSavForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addGamePageSavSubmit(addGameSavRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="addGameSavDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>

    <el-dialog v-model="editGameSavDialog" title="编辑推荐录像">
        <el-form :model="editGameSavForm" :rules="editGameSavFormRules" ref="editGameSavRuleFormRef"
            class="edit-banner-form" status-icon>
            <el-form-item label="ID" prop="id" required>
                <el-input placeholder="ID" v-model="editGameSavForm.id" type="text" disabled />
            </el-form-item>
            <el-form-item label="P1_ID" prop="p1_id" required>
                <el-input placeholder="请输入P1用户ID" v-model="editGameSavForm.p1_id" type="text" maxlength="30" size="large" />
            </el-form-item>
            <el-form-item label="P1_ID" prop="p2_id" required>
                <el-input placeholder="请输入P2用户ID" v-model="editGameSavForm.p2_id" type="text" maxlength="30" size="large" />
            </el-form-item>
            <el-form-item label="P1比分" prop="p1_win_num" required>
                <el-input placeholder="请输入P1比分" v-model="editGameSavForm.p1_win_num" type="text" maxlength="30"
                    size="large" />
            </el-form-item>
            <el-form-item label="P2比分" prop="p2_win_num" required>
                <el-input placeholder="请输入P2比分" v-model="editGameSavForm.p2_win_num" type="text" maxlength="30"
                    size="large" />
            </el-form-item>
            <el-form-item label="录像ID" prop="sav_ids" required>
                <el-input type="textarea" v-model="editGameSavForm.sav_ids" :rows="4" placeholder="请输入录像ID"
                    size="large"></el-input>
            </el-form-item>
            <el-form-item label="货币" prop="currency" required>
                <el-select v-model="editGameSavForm.currency" placeholder="请选择货币" size="large" disabled>
                    <el-option v-for="(item, index) in mainStore.recSavCurrency" :key="item.id" :label="item.name"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="价格" prop="price" required>
                <el-input v-model="editGameSavForm.price" :rows="4" placeholder="请输入价格" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="原价" prop="origin_price" required>
                <el-input v-model="editGameSavForm.origin_price" :rows="4" placeholder="请输入原价" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="展示">
                <el-switch v-model="editGameSavForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="editGamePageSavSubmit(editGameSavRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="editGameSavDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped></style>