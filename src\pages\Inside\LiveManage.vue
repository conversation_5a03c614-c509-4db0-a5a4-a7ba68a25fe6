<script setup lang="ts">
import { ArrowRight, House, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox, genFileId } from 'element-plus';
import { UserLiveItem } from '../../interface';

const mainStore = useMainStore();

const addLiveDialog = ref(false);
const editLiveDialog = ref(false);
const loading = ref(false);
const currentPage = ref(1);

const userLiveData = reactive({
    total: 0,
    data: [] as Array<UserLiveItem>
});

const liveLevel = [
    {
        value: 1,
    },
    {
        value: 2,
    },
    {
        value: 3,
    },
    {
        value: 4,
    },
    {
        value: 5,
    },
    {
        value: 6,
    },
    {
        value: 7,
    },
    {
        value: 8,
    },
    {
        value: 9,
    },
];
const liveType = [
    {
        value: 0,
        label: '外链直播'
    },
    {
        value: 1,
        label: '内部直播'
    }
];
const liveState = [
    {
        value: 0,
        label: '默认'
    },
    {
        value: 1,
        label: '直播中'
    },
    {
        value: 2,
        label: '离线'
    },
];


const addLiveBtnLock = ref(true);
const addLiveForm = reactive({
    user_id: '',
    url: '',
    icon_url: '',
    level: 1,
    type: 0,
    state: 0,
});
const addLiveRuleFormRef = ref<FormInstance>();
const addLiveFormRules = reactive<FormRules>({
    user_id: [
        {
            required: true, message: '请输入玩家ID', trigger: 'blur'
        }
    ],
    url: [
        {
            required: true, message: '请输入直播链接', trigger: 'blur'
        }
    ],
    level: [
        {
            required: true, message: '请选择直播等级', trigger: 'blur'
        }
    ],
});

const editLiveBtnLock = ref(true);
const editLiveForm = reactive({
    user_id: '' as number | string,
    url: '',
    icon_url: '',
    level: 1,
    type: 0,
    state: 0,
});
const editLiveRuleFormRef = ref<FormInstance>();
const editLiveFormRules = reactive<FormRules>({
    game_id: [
        {
            required: true, message: '请选择游戏', trigger: 'blur'
        }
    ],
    sav_ids: [
        {
            required: true, message: '请输入录像ID', trigger: 'blur'
        }
    ],
    banner_url: [
        {
            required: true, message: '请上传PC版banner', trigger: 'blur'
        }
    ],
    banner_url_m: [
        {
            required: true, message: '请上传手机版banner', trigger: 'blur'
        }
    ],
});

const addUploadRef = ref<UploadInstance>();
const editUploadRef = ref<UploadInstance>();

getUserLiveList();

async function getUserLiveList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${currentPage.value}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getUserLiveList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            currentPage: currentPage.value,
            token: token,
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.liveList) {
                    userLiveData.total = res.data.total;
                    userLiveData.data = res.data.liveList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }

}

function addLive() {
    addLiveDialog.value = true;
}

async function addLiveSubmit(formEl: FormInstance | undefined) {
    if (!addLiveBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            const targetUserID = parseInt(addLiveForm.user_id);
            if (!targetUserID || targetUserID < 0) {
                return mainStore.globalMessageTip('玩家ID错误', 3);
            }
            if (!addLiveForm.url) {
                return mainStore.globalMessageTip('请输入直播间链接', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${targetUserID}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            addLiveBtnLock.value = false;
            await requset.addUserLive({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: targetUserID,
                liveLevel: addLiveForm.level,
                liveState: addLiveForm.state,
                liveType: addLiveForm.type,
                liveUrl: addLiveForm.url,
                iconUrl: addLiveForm.icon_url,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('添加成功!', 0);
                    addLiveDialog.value = false;
                    addLiveForm.icon_url = '';
                    addLiveForm.url = '';
                    addLiveForm.user_id = '';
                    addLiveForm.level = 1;
                    addLiveForm.type = 0;
                    addLiveForm.state = 0;
                    getUserLiveList();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('推荐录像数量已达上限!', 3);
                                break;
                            default:
                                break;
                        }
                    }
                }
                mainStore.globalLoading(false);
                addLiveBtnLock.value = true;
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
                mainStore.globalLoading(false);
                addLiveBtnLock.value = true;
            });
        } else {
            return;
        }
    });
}

function editLive(item: UserLiveItem) {
    editUploadRef.value?.clearFiles();
    editLiveForm.user_id = item.user_id;
    editLiveForm.url = item.url;
    editLiveForm.icon_url = item.icon_url;
    editLiveForm.type = item.type;
    editLiveForm.level = item.level;
    editLiveDialog.value = true;
}

async function deleteLive(user_id: number) {
    if (!user_id) {
        return;
    }
    ElMessageBox.confirm(`确定要删除该用户的直播间?`, '删除推荐录像', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${user_id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.deleteUserLiveRoom({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetUserID: user_id,
            token: token
        }).then(async (res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('删除成功', 0);
                getUserLiveList();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该用户没有直播间', 3);
                            getUserLiveList();
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });

}


async function editLiveSubmit(formEl: FormInstance | undefined) {
    if (!editLiveBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            const targetUserID = parseInt(String(editLiveForm.user_id));
            if (!targetUserID || targetUserID < 0) {
                return mainStore.globalMessageTip('玩家ID错误', 3);
            }
            if (!editLiveForm.url) {
                return mainStore.globalMessageTip('请输入直播间链接', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${targetUserID}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            editLiveBtnLock.value = false;
            await requset.editUserLive({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: targetUserID,
                liveType: editLiveForm.type,
                liveLevel: editLiveForm.level,
                liveState: editLiveForm.state,
                liveUrl: editLiveForm.url,
                iconUrl: editLiveForm.icon_url,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('修改成功!', 0);
                    editLiveDialog.value = false;
                    editLiveForm.user_id = '';
                    editLiveForm.url = '';
                    editLiveForm.icon_url = '';
                    editLiveForm.type = 0;
                    editLiveForm.state = 0;
                    editLiveForm.level = 1;
                    editUploadRef.value?.clearFiles();
                    getUserLiveList();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该用户直播间不存在!', 3);
                                getUserLiveList();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            editLiveBtnLock.value = true;
        } else {
            return;
        }
    });
}

function liveIconUploadBefore(file: any) {
    console.log(file);
    if (file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'image/jpeg') {
        mainStore.globalMessageTip('只能上传PNG/JPG类型图片', 3);
        return false;
    }
    if (file.size > 5120000) {
        mainStore.globalMessageTip('文件太大了', 3);
        return false;
    }
}

const addUploadExceed: UploadProps['onExceed'] = (files) => {
    addUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    addUploadRef.value!.handleStart(file);
    addUploadRef.value!.submit();
}

const editUploadExceed: UploadProps['onExceed'] = (files) => {
    editUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    editUploadRef.value!.handleStart(file);
    editUploadRef.value!.submit();
}

function addLiveUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                addLiveForm.icon_url = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function editLiveUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                editLiveForm.icon_url = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}


function liveIconUploadError(err: any) {
    mainStore.globalMessageTip(err, 3);
}

async function currentPageChange(page: any) {
    currentPage.value = page;
    await getUserLiveList();
}

function getLiveTypeName(value: number) {
    const index = liveType.findIndex(item => item.value === value);
    if (index >= 0) {
        return liveType[index].label;
    }
    return value;
}

function getLiveStateName(value: number) {
    const index = liveState.findIndex(item => item.value === value);
    if (index >= 0) {
        return liveState[index].label;
    }
    return value;
}

</script>

<template>
    <div class="banner-config" v-if="mainStore.checkPermission(2008)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'inside'
                }">
                    约战内部
                </el-breadcrumb-item>
                <el-breadcrumb-item>直播间管理</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-button type="primary" size="large" @click="addLive()">添加直播间
                                </el-button>
                                <el-button type="primary" size="large" @click="getUserLiveList()">刷新
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table v-loading="loading" :data="userLiveData.data" border style="width: 100%">
                        <el-table-column prop="user_id" label="用户ID" />
                        <el-table-column label="玩家昵称">
                            <template #default="scope">
                                <el-link type="primary" :underline="false"
                                    @click="mainStore.gotoUserDetail(scope.row.user_id)">{{ scope.row.nickname }}
                                </el-link>
                            </template>
                        </el-table-column>
                        <el-table-column label="直播类型">
                            <template #default="scope">
                                <span>{{ getLiveTypeName(scope.row.type) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="level" label="直播等级" />
                        <el-table-column label="直播状态">
                            <template #default="scope">
                                <span>{{ getLiveStateName(scope.row.state) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="url" label="直播间链接" />
                        <el-table-column label="直播图标">
                            <template #default="scope">
                                <el-image fit="contain" :src="scope.row.icon_url">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                            </template>
                        </el-table-column>
                        <el-table-column prop="handler" label="处理人" />
                        <el-table-column prop="update_time" label="更新时间" />
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="info" @click="editLive(scope.row)">编辑</el-link>
                                    <el-link type="danger" @click="deleteLive(scope.row.user_id)">删除</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination background layout="prev, pager, next" :total="userLiveData.total"
                        :current-page="currentPage" :page-size="15" @current-change="currentPageChange" />
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="addLiveDialog" title="添加直播间">
        <el-form :model="addLiveForm" :rules="addLiveFormRules" ref="addLiveRuleFormRef" class="add-banner-form"
            status-icon>
            <el-form-item label="玩家ID" prop="user_id" required>
                <el-input placeholder="请输入玩家ID" v-model="addLiveForm.user_id" type="text" maxlength="30" size="large" />
            </el-form-item>
            <el-form-item label="直播链接" prop="url" required>
                <el-input type="textarea" v-model="addLiveForm.url" :rows="3" placeholder="请输入直播间链接" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="直播类型" prop="type" required>
                <el-select v-model="addLiveForm.type" placeholder="选择直播类型" size="large">
                    <el-option v-for="item in liveType" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="直播等级" prop="level" required>
                <el-select v-model="addLiveForm.level" placeholder="选择直播等级" size="large">
                    <el-option v-for="item in liveLevel" :key="item.value" :label="item.value" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="直播状态" prop="state" required>
                <el-select v-model="addLiveForm.state" placeholder="选择直播状态" size="large">
                    <el-option v-for="item in liveState" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="直播图标">
                <el-upload class="upload-demo" drag accept=".jpeg, .png, .jpg" :limit="1" :on-exceed="addUploadExceed"
                    ref="addUploadRef" :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :before-upload="liveIconUploadBefore" :on-success="addLiveUploadSuccess"
                    :on-error="liveIconUploadError" size="large">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%">
                    <el-input type="textarea" v-model="addLiveForm.icon_url" :rows="3" placeholder="直播图标链接(可选)"
                        size="large">
                    </el-input>
                </div>
                <div class="block" style="width: 100%">
                    <el-image fit="contain" :src="addLiveForm.icon_url">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addLiveSubmit(addLiveRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="addLiveDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>

    <el-dialog v-model="editLiveDialog" title="编辑直播间">
        <el-form :model="editLiveForm" :rules="editLiveFormRules" ref="editLiveRuleFormRef" class="edit-banner-form"
            status-icon>
            <el-form-item label="玩家ID" prop="user_id" required>
                <el-input placeholder="请输入玩家ID" v-model="editLiveForm.user_id" type="text" maxlength="30" size="large"
                    disabled />
            </el-form-item>
            <el-form-item label="直播链接" prop="url" required>
                <el-input type="textarea" v-model="editLiveForm.url" :rows="3" placeholder="请输入直播间链接" size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="直播类型" prop="type" required>
                <el-select v-model="editLiveForm.type" placeholder="选择直播类型" size="large">
                    <el-option v-for="item in liveType" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="直播等级" prop="level" required>
                <el-select v-model="editLiveForm.level" placeholder="选择直播等级" size="large">
                    <el-option v-for="item in liveLevel" :key="item.value" :label="item.value" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="直播状态" prop="state" required>
                <el-select v-model="editLiveForm.state" placeholder="选择直播状态" size="large">
                    <el-option v-for="item in liveState" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="直播图标">
                <el-upload class="upload-demo" drag accept=".jpeg, .png, .jpg" :limit="1" :on-exceed="editUploadExceed"
                    ref="editUploadRef" :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :before-upload="liveIconUploadBefore" :on-success="editLiveUploadSuccess"
                    :on-error="liveIconUploadError" size="large">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%">
                    <el-input type="textarea" v-model="editLiveForm.icon_url" :rows="3" placeholder="直播图标链接(可选)"
                        size="large">
                    </el-input>
                </div>
                <div class="block" style="width: 100%">
                    <el-image fit="contain" :src="editLiveForm.icon_url">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="editLiveSubmit(editLiveRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="editLiveDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped>

</style>