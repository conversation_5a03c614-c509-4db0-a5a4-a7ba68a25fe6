<script setup lang="ts">
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { useRoute } from 'vue-router';
import { router } from '../../router';
import { Warning, SwitchButton, User, Menu, Avatar, Promotion, BellFilled, Flag, VideoCameraFilled } from '@element-plus/icons-vue';
import crypto from 'crypto-js';
import requset from '../../api';

const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

getAllReviewNum();

function menuGoRoute(name: string) {
    if (route.name === name) {
        return;
    }
    router.push({
        name: name
    });
}

async function getAllReviewNum() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        requset.getAllReviewNum({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token
        }).then(res => {
            if (res.code === 0 && res.data) {
                outsourcingStore.$patch({
                    reportReviewNum: res.data.reportReviewNum,
                    nicknameReviewNum: res.data.nicknameReviewNum,
                    avatarReviewNum: res.data.avatarReviewNum,
                    reportReviewProcessed: res.data.reportProcessed,
                    nicknameReviewProcessed: res.data.nicknameProcessed,
                    avatarReviewProcessed: res.data.avatarProcessed
                });
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

</script>

<template>
    <el-aside class="admin-aside">
        <el-menu class="admin-menu" :default-active="route.name" text-color="#fff" background-color="#333744"
            v-if="mainStore.queryMenuPermission(mainStore.menuList, 1000)">
            <el-sub-menu index="1" v-if="mainStore.queryMenuPermission(mainStore.menuList, 1100)">
                <template #title>
                    <el-icon>
                        <Warning />
                    </el-icon>
                    <span>举报管理</span>
                    <el-tag effect="dark" size="small" type="danger">{{
                            outsourcingStore.reportReviewNum + outsourcingStore.nicknameReviewNum +
                            outsourcingStore.avatarReviewNum
                    }}</el-tag>
                </template>
                <el-menu-item index="reportReview" @click="menuGoRoute('reportReview')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 1101)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>举报处理</span>
                    <el-tag effect="dark" size="small" type="danger">{{ outsourcingStore.reportReviewNum }}</el-tag>
                </el-menu-item>
                <el-menu-item index="nicknameReview" @click="menuGoRoute('nicknameReview')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 1102)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>昵称审核</span>
                    <el-tag effect="dark" size="small" type="danger">{{ outsourcingStore.nicknameReviewNum }}</el-tag>
                </el-menu-item>
                <el-menu-item index="avatarReview" @click="menuGoRoute('avatarReview')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 1103)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>头像审核</span>
                    <el-tag effect="dark" size="small" type="danger">{{ outsourcingStore.avatarReviewNum }}</el-tag>
                </el-menu-item>
            </el-sub-menu>
            <el-menu-item index="accountSearch" @click="menuGoRoute('accountSearch')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 1001)">
                <el-icon>
                    <Avatar />
                </el-icon>
                <span>账户查询</span>
            </el-menu-item>
            <el-menu-item index="ipAccountSearch" @click="menuGoRoute('ipAccountSearch')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 1004)">
                <el-icon>
                    <Avatar />
                </el-icon>
                <span>IP查询</span>
            </el-menu-item>
            <el-menu-item index="serverNotice" @click="menuGoRoute('serverNotice')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 1002)">
                <el-icon>
                    <BellFilled />
                </el-icon>
                <span>全区公告</span>
            </el-menu-item>
            
            <!-- <el-menu-item index="kickPlayer" @click="menuGoRoute('kickPlayer')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 1003)">
                <el-icon>
                    <Promotion />
                </el-icon>
                <span>踢人功能</span>
            </el-menu-item> -->
        </el-menu>
    </el-aside>
    <el-main class="admin-content">
        <router-view v-if="mainStore.checkPermission(1000)"></router-view>
        <h2 v-else>权限不足, 请联系管理员!</h2>
    </el-main>
</template>

<style lang="less" scoped>

</style>