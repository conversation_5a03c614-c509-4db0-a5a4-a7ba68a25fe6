<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ArrowRight, House } from '@element-plus/icons-vue';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { onMounted, reactive, ref } from 'vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { router } from '../../router';
import { CoopGameInfo, Forbid, HackCheckRecord, IpInfo, IpQuery, OldNicknameItem, RecentLoginItem, TalkMsg, UserAvatar, UserTaskRecord, VsGameInfo } from '../../interface';
import axios from "axios";
import { ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import * as echarts from 'echarts/core';
import {
    TitleComponent,
    TitleComponentOption,
    ToolboxComponent,
    ToolboxComponentOption,
    TooltipComponent,
    TooltipComponentOption,
    GridComponent,
    GridComponentOption,
    LegendComponent,
    LegendComponentOption
} from 'echarts/components';
import {
    LineChart,
    LineSeriesOption
} from 'echarts/charts';
import {
    UniversalTransition
} from 'echarts/features';
import {
    CanvasRenderer
} from 'echarts/renderers';

echarts.use(
    [TitleComponent, ToolboxComponent, TooltipComponent, GridComponent, LegendComponent, LineChart, CanvasRenderer, UniversalTransition]
);



const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const tabIndex = ref('day');
const countType = ref(1);

const dayRechargeFetchFlag = ref(false);
const weekRechargeFetchFlag = ref(false);
const monthRechargeFetchFlag = ref(false);

const dayFilterForm = reactive({
    platform: -1,
    date_range: ['', ''],
    quick_select: 1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
    settlement_type: mainStore.channelCountType[0] ? mainStore.channelCountType[0].value : '',
});
const dayRechargeData = reactive({
    recharge_user: 0,
    recharge_num: 0,
    recharge_new: 0,
    cny_total: 0,
    usd_total: 0
});

const weekFilterForm = reactive({
    platform: -1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
    settlement_type: mainStore.channelCountType[0] ? mainStore.channelCountType[0].value : '',
});
const weekRechargeData = reactive({
    recharge_user: 0,
    recharge_num: 0,
    recharge_new: 0,
    cny_total: 0,
    usd_total: 0
});

const monthFilterForm = reactive({
    platform: -1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
    settlement_type: mainStore.channelCountType[0] ? mainStore.channelCountType[0].value : '',
});
const monthRechargeData = reactive({
    recharge_user: 0,
    recharge_num: 0,
    recharge_new: 0,
    cny_total: 0,
    usd_total: 0
});

mainStore.$subscribe((mutation, state) => {
    if (state.channelList.length > 0) {
        dayFilterForm.app_channel = mainStore.channelList[0].channel_id;
        weekFilterForm.app_channel = mainStore.channelList[0].channel_id;
        monthFilterForm.app_channel = mainStore.channelList[0].channel_id;
    }
    if (state.channelCountType.length > 0) {
        dayFilterForm.settlement_type = mainStore.channelCountType[0].value;
        weekFilterForm.settlement_type = mainStore.channelCountType[0].value;
        monthFilterForm.settlement_type = mainStore.channelCountType[0].value;
    }
});

dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
dayFilterForm.date_range[1] = mainStore.quckGetDate(1);


async function statisticsRechargeData(count_type: number) {

    let appChannel = 0;
    let platform = -1;
    let start_time = '';
    let end_time = '';
    let settlement_type = 0;
    switch (count_type) {
        case 1:
            appChannel = parseInt(String(dayFilterForm.app_channel));
            platform = dayFilterForm.platform;
            start_time = dayFilterForm.date_range[0];
            end_time = dayFilterForm.date_range[1];
            settlement_type = parseInt(String(dayFilterForm.settlement_type));
            break;
        case 2:
            appChannel = parseInt(String(weekFilterForm.app_channel));
            platform = weekFilterForm.platform;
            settlement_type = parseInt(String(weekFilterForm.settlement_type));
            break;
        case 3:
            appChannel = parseInt(String(monthFilterForm.app_channel));
            platform = monthFilterForm.platform;
            settlement_type = parseInt(String(monthFilterForm.settlement_type));
            break;
        default:
            break;
    }

    if (!appChannel) {
        return mainStore.globalMessageTip('请选择渠道', 3);
    }

    if (!settlement_type) {
        return mainStore.globalMessageTip('请选择结算类型', 3);
    }

    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${count_type}${appChannel}${platform}${settlement_type}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.statisticsRechargeData({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            countType: count_type,
            appChannel: appChannel,
            platForm: platform,
            startTime: start_time,
            endTime: end_time,
            settlementType: settlement_type,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                if (res.data) {
                    switch (count_type) {
                        case 1:
                            dayRechargeFetchFlag.value = true;
                            setTimeout(() => {
                                renderDayChart(res.data.xData, res.data.rechargeUserData, res.data.rechargeNumData, res.data.rechargeNewData, res.data.cnyData);
                                dayRechargeData.recharge_user = res.data.rechargeUser;
                                dayRechargeData.recharge_num = res.data.rechargeNum;
                                dayRechargeData.recharge_new = res.data.rechargeNew;
                                dayRechargeData.cny_total = res.data.cnyTotal;
                                dayRechargeData.usd_total = res.data.usdTotal;
                            }, 0);

                            break;
                        case 2:
                            weekRechargeFetchFlag.value = true;
                            setTimeout(() => {
                                renderWeekChart(res.data.xData, res.data.rechargeUserData, res.data.rechargeNumData, res.data.rechargeNewData, res.data.cnyData);
                                weekRechargeData.recharge_user = res.data.rechargeUser;
                                weekRechargeData.recharge_num = res.data.rechargeNum;
                                weekRechargeData.recharge_new = res.data.rechargeNew;
                                weekRechargeData.cny_total = res.data.cnyTotal;
                                weekRechargeData.usd_total = res.data.usdTotal;
                            }, 0);
                            break;
                        case 3:
                            monthRechargeFetchFlag.value = true;
                            setTimeout(() => {
                                renderMonthChart(res.data.xData, res.data.rechargeUserData, res.data.rechargeNumData, res.data.rechargeNewData, res.data.cnyData);
                                monthRechargeData.recharge_user = res.data.rechargeUser;
                                monthRechargeData.recharge_num = res.data.rechargeNum;
                                monthRechargeData.recharge_new = res.data.rechargeNew;
                                monthRechargeData.cny_total = res.data.cnyTotal;
                                monthRechargeData.usd_total = res.data.usdTotal;
                            }, 0);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function dayTimeRadioChange(val: any) {
    switch (val) {
        case 1:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(1);
            break;
        case 2:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(2);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 3:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(3);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 4:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(4);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        default:
            break;
    }
}

function daySettlementTypeChange(val: any) {
    dayFilterForm.settlement_type = val;
}
function weekSettlementTypeChange(val: any) {
    weekFilterForm.settlement_type = val;
}

function monthSettlementTypeChange(val: any) {
    monthFilterForm.settlement_type = val;
}

function renderDayChart(xData: Array<any>, data1: Array<number>, data2: Array<number>, data3: Array<number>, data4: Array<number>) {
    const dayRechargeDataCanvas = document.getElementById('dayRechargeDataCanvas');
    const dayIncomeDataCanvas = document.getElementById('dayIncomeDataCanvas');
    if (dayRechargeDataCanvas) {
        const dayRechargeChart = echarts.init(dayRechargeDataCanvas);
        const option = {
            title: {
                text: '充值'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['充值人数', '充值次数', '首冲人数']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '充值人数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data1,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
                {
                    name: '充值次数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data2,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
                {
                    name: '首冲人数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data3,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
            ]
        };
        option && dayRechargeChart.setOption(option);
    }
    if (dayIncomeDataCanvas) {
        const dayIncomeChart = echarts.init(dayIncomeDataCanvas);
        const option = {
            title: {
                text: '收入'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['消费金额']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '消费金额',
                    type: 'line',
                    smooth: true,
                    areaStyle: {},
                    data: data4,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                },
            ]
        };
        option && dayIncomeChart.setOption(option);
    }
}

function renderWeekChart(xData: Array<any>, data1: Array<number>, data2: Array<number>, data3: Array<number>, data4: Array<number>) {
    const weekRechargeDataCanvas = document.getElementById('weekRechargeDataCanvas');
    const weekIncomeDataCanvas = document.getElementById('weekIncomeDataCanvas');
    if (weekRechargeDataCanvas) {
        const weekRechargeChart = echarts.init(weekRechargeDataCanvas);
        const option = {
            title: {
                text: '周充值'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['充值人数', '充值次数', '首冲人数']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '充值人数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data1,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
                {
                    name: '充值次数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data2,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
                {
                    name: '首冲人数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data3,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
            ]
        };
        option && weekRechargeChart.setOption(option);
    }
    if (weekIncomeDataCanvas) {
        const weekIncomeChart = echarts.init(weekIncomeDataCanvas);
        const option = {
            title: {
                text: '周收入'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['消费金额']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '消费金额',
                    type: 'line',
                    smooth: true,
                    areaStyle: {},
                    data: data4,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                },
            ]
        };
        option && weekIncomeChart.setOption(option);
    }
}


function renderMonthChart(xData: Array<any>, data1: Array<number>, data2: Array<number>, data3: Array<number>, data4: Array<number>) {
    const monthRechargeDataCanvas = document.getElementById('monthRechargeDataCanvas');
    const monthIncomeDataCanvas = document.getElementById('monthIncomeDataCanvas');
    if (monthRechargeDataCanvas) {
        const monthRechargeChart = echarts.init(monthRechargeDataCanvas);
        const option = {
            title: {
                text: '月充值'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['充值人数', '充值次数', '首冲人数']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '充值人数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data1,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
                {
                    name: '充值次数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data2,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
                {
                    name: '首冲人数',
                    type: 'line',
                    // stack: 'Total',
                    smooth: true,
                    data: data3,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                },
            ]
        };
        option && monthRechargeChart.setOption(option);
    }
    if (monthIncomeDataCanvas) {
        const monthIncomeChart = echarts.init(monthIncomeDataCanvas);
        const option = {
            title: {
                text: '月收入'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['消费金额']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xData
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '消费金额',
                    type: 'line',
                    smooth: true,
                    areaStyle: {},
                    data: data4,
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    emphasis: {
                        focus: 'series'
                    },
                },
            ]
        };
        option && monthIncomeChart.setOption(option);
    }
}
</script>

<template>
    <div class="user-detai" v-if="mainStore.checkPermission(4201)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'statistics'
                }">
                    数据统计
                </el-breadcrumb-item>
                <el-breadcrumb-item>收入数据</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill :size="32">
                    <el-tabs type="border-card" v-model="tabIndex">
                        <el-tab-pane label="日/小时收入" name="day">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="dayFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="时间" size="large">
                                                <el-col :span="7">
                                                    <el-date-picker v-model="dayFilterForm.date_range" type="daterange"
                                                        start-placeholder="开始日期" end-placeholder="结束日期" size="large"
                                                        value-format="YYYY-MM-DD"
                                                        :default-value="[new Date(), new Date()]" />
                                                </el-col>
                                                <el-col :span="8">
                                                    <el-radio-group v-model="dayFilterForm.quick_select" size="large"
                                                        @change="dayTimeRadioChange">
                                                        <el-radio :label="1" size="large">今日</el-radio>
                                                        <el-radio :label="2" size="large">昨日</el-radio>
                                                        <el-radio :label="3" size="large">最近7天</el-radio>
                                                        <el-radio :label="4" size="large">最近30天</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="dayFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="结算" size="large">
                                                <el-col :span="6">
                                                    <el-radio-group v-model="dayFilterForm.settlement_type" size="large"
                                                        @change="daySettlementTypeChange">
                                                        <el-radio :label="item.value" size="large"
                                                            v-for="(item, index) in mainStore.channelCountType"
                                                            :key="item.value">{{item.label}}</el-radio>
                                                    </el-radio-group>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click="statisticsRechargeData(1)"
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>

                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="dayRechargeDataCanvas" style="height: 400px;" v-if="dayRechargeFetchFlag">
                                </div>
                                <div id="dayIncomeDataCanvas" style="height: 400px;" v-if="dayRechargeFetchFlag"></div>
                                <el-row v-if="dayRechargeFetchFlag">
                                    总计：充值人数&nbsp;<span>{{dayRechargeData.recharge_user}}</span>&nbsp;人，充值次数&nbsp;<span>{{dayRechargeData.recharge_num}}</span>&nbsp;次，首充人数&nbsp;<span>{{dayRechargeData.recharge_new}}</span>&nbsp;人，总收入&nbsp;<span>{{dayRechargeData.cny_total}}</span>&nbsp;元。
                                </el-row>
                            </el-space>

                        </el-tab-pane>
                        <el-tab-pane label="周收入" name="week">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="weekFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="weekFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="结算" size="large">
                                                <el-col :span="6">
                                                    <el-radio-group v-model="weekFilterForm.settlement_type"
                                                        size="large" @change="weekSettlementTypeChange">
                                                        <el-radio :label="item.value" size="large"
                                                            v-for="(item, index) in mainStore.channelCountType"
                                                            :key="item.value">{{item.label}}</el-radio>
                                                    </el-radio-group>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click="statisticsRechargeData(2)"
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>

                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="weekRechargeDataCanvas" style="height: 400px;" v-if="weekRechargeFetchFlag">
                                </div>
                                <div id="weekIncomeDataCanvas" style="height: 400px;" v-if="weekRechargeFetchFlag">
                                </div>
                                <el-row v-if="weekRechargeFetchFlag">
                                    总计：充值人数&nbsp;<span>{{weekRechargeData.recharge_user}}</span>&nbsp;人，充值次数&nbsp;<span>{{weekRechargeData.recharge_num}}</span>&nbsp;次，首充人数&nbsp;<span>{{weekRechargeData.recharge_new}}</span>&nbsp;人，总收入&nbsp;<span>{{weekRechargeData.cny_total}}</span>&nbsp;元。
                                </el-row>
                            </el-space>

                        </el-tab-pane>
                        <el-tab-pane label="月收入" name="month">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="monthFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="monthFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="结算" size="large">
                                                <el-col :span="6">
                                                    <el-radio-group v-model="monthFilterForm.settlement_type"
                                                        size="large" @change="monthSettlementTypeChange">
                                                        <el-radio :label="item.value" size="large"
                                                            v-for="(item, index) in mainStore.channelCountType"
                                                            :key="item.value">{{item.label}}</el-radio>
                                                    </el-radio-group>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click="statisticsRechargeData(3)"
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>

                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="monthRechargeDataCanvas" style="height: 400px;" v-if="monthRechargeFetchFlag">
                                </div>
                                <div id="monthIncomeDataCanvas" style="height: 400px;" v-if="monthRechargeFetchFlag">
                                </div>
                                <el-row v-if="monthRechargeFetchFlag">
                                    总计：充值人数&nbsp;<span>{{monthRechargeData.recharge_user}}</span>&nbsp;人，充值次数&nbsp;<span>{{monthRechargeData.recharge_num}}</span>&nbsp;次，首充人数&nbsp;<span>{{monthRechargeData.recharge_new}}</span>&nbsp;人，总收入&nbsp;<span>{{monthRechargeData.cny_total}}</span>&nbsp;元。
                                </el-row>
                            </el-space>

                        </el-tab-pane>
                    </el-tabs>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped>

</style>