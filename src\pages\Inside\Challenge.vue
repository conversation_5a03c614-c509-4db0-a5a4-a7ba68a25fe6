<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import { FormInstance, FormRules, ElMessageBox } from 'element-plus';
import { router } from '../../router';
import { ChallengeUser } from '../../interface';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const currentPage = ref(1);
const banUserDialog = ref(false);
const unfreezeUserDialog = ref(false);

const filterForm = reactive({
    account: '',
    user_id: '',
});

const challengeUsers = reactive({
    total: 0,
    data: [] as Array<ChallengeUser>
});

getChallengeUserList();
async function getChallengeUserList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${currentPage.value}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;

        await requset.getChallengeUserList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            currentPage: currentPage.value,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    challengeUsers.total = res.data.total;
                    challengeUsers.data = res.data.challengeUserList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function addChallengeRoomUser() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);

        await requset.addChallengeRoomUser({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_account: filterForm.account,
            target_userID: parseInt(filterForm.user_id),
            token: token
        }).then(res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('添加成功', 0);
                getChallengeUserList();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('用户不存在', 3);
                            break;
                        case 6:
                            mainStore.globalMessageTip('该用户已有挑战房权限', 3);
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch(err => {
            console.log(err);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function currentPageChange(page: any) {
    currentPage.value = page;
    await getChallengeUserList();
}

async function cancelUserChallenge(item: ChallengeUser) {
    if (!item.user_id || item.user_id <= 0) {
        return;
    }
    ElMessageBox.confirm(`确定取消: ${item.nickname} 玩家挑战房权限?`, '取消挑战房权限', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${item.user_id}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.cancelUserChallenge({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: item.user_id,
                token: token
            }).then(async res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0); getChallengeUserList();
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch(err => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

</script>

<template>
    <div class="account-search" v-if="mainStore.checkPermission(2005)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'inside'
                }">
                    约战内部
                </el-breadcrumb-item>
                <el-breadcrumb-item>挑战房权限</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 50%" fill>
                    <el-form ref="ruleFormRef" :model="filterForm" status-icon class="login-form">
                        <el-form-item prop="account" label="玩家ID" label-width="120px">
                            <el-input placeholder="请输入玩家ID" v-model="filterForm.user_id" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item prop="account" label="玩家账号" label-width="120px">
                            <el-input placeholder="请输入玩家账号" v-model="filterForm.account" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item prop="account" label-width="120px">
                            <el-button type="primary" size="large" @click="addChallengeRoomUser()">提交</el-button>
                        </el-form-item>
                    </el-form>
                </el-space>
                <el-table :data="challengeUsers.data" border style="width: 100%">
                    <el-table-column label="玩家昵称">
                        <template #default="scope">
                            <el-link type="primary" :underline="false"
                                @click="mainStore.gotoUserDetail(scope.row.user_id)">{{scope.row.nickname}}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="user_id" label="用户ID" />
                    <el-table-column prop="last_login" label="最后登录" />
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="danger" @click="cancelUserChallenge(scope.row)">取消
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination background layout="prev, pager, next" :total="challengeUsers.total"
                    :current-page="currentPage" @current-change="currentPageChange" />

            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped>

</style>