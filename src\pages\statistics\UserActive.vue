<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ArrowRight, House } from '@element-plus/icons-vue';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { onMounted, reactive, ref } from 'vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { router } from '../../router';
import { CoopGameInfo, Forbid, HackCheckRecord, IpInfo, IpQuery, OldNicknameItem, RecentLoginItem, TalkMsg, UserAvatar, UserTaskRecord, VsGameInfo } from '../../interface';
import axios from "axios";
import { ElMessageBox } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import * as echarts from 'echarts/core';
import {
    TitleComponent,
    TooltipComponent,
    TooltipComponentOption,
    GridComponent,
    GridComponentOption,
    LegendComponent,
    LegendComponentOption,
    MarkLineComponent,
    MarkLineComponentOption,
    ToolboxComponent
} from 'echarts/components';
import {
    BarChart,
    BarSeriesOption
} from 'echarts/charts';
import {
    CanvasRenderer
} from 'echarts/renderers';

echarts.use([
    TitleComponent,
    TooltipComponent,
    ToolboxComponent,
    GridComponent,
    LegendComponent,
    MarkLineComponent,
    BarChart,
    CanvasRenderer
]);


const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const tabIndex = ref('day');
const countType = ref(1);

const dayActiveFetchFlag = ref(false);
const weekActiveFetchFlag = ref(false);
const monthActiveFetchFlag = ref(false);

const dayFilterForm = reactive({
    platform: -1,
    date_range: ['', ''],
    quick_select: 1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
});
const weekFilterForm = reactive({
    platform: -1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
});
const monthFilterForm = reactive({
    platform: -1,
    app_channel: mainStore.channelList[0] ? mainStore.channelList[0].channel_id : '',
});

mainStore.$subscribe((mutation, state) => {
    if (state.channelList.length > 0) {
        dayFilterForm.app_channel = mainStore.channelList[0].channel_id;
        weekFilterForm.app_channel = mainStore.channelList[0].channel_id;
        monthFilterForm.app_channel = mainStore.channelList[0].channel_id;
    }
});

dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
dayFilterForm.date_range[1] = mainStore.quckGetDate(1);


async function statisticsUserActiveData(count_type: number) {

    let appChannel = 0;
    let platform = -1;
    let start_time = '';
    let end_time = '';

    switch (countType.value) {
        case 1:
            appChannel = parseInt(String(dayFilterForm.app_channel));
            platform = dayFilterForm.platform;
            start_time = dayFilterForm.date_range[0];
            end_time = dayFilterForm.date_range[1];
            break;
        case 2:
            appChannel = parseInt(String(weekFilterForm.app_channel));
            platform = weekFilterForm.platform;
            break;
        case 3:
            appChannel = parseInt(String(monthFilterForm.app_channel));
            platform = monthFilterForm.platform;
            break;
        default:
            break;
    }

    if (!appChannel) {
        return mainStore.globalMessageTip('请选择渠道', 3);
    }

    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${count_type}${appChannel}${platform}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.statisticsUserActiveData({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            countType: count_type,
            appChannel: appChannel,
            platForm: platform,
            startTime: start_time,
            endTime: end_time,
            token: token
        }).then(async res => {
            if (res.code === 0) {
                if (res.data) {
                    switch (count_type) {
                        case 1:
                            dayActiveFetchFlag.value = true;
                            setTimeout(() => {
                                renderDayChart(res.data.xData, res.data.newAddData, res.data.activeData);
                            }, 0);
                            break;
                        case 2:
                            weekActiveFetchFlag.value = true;
                            setTimeout(() => {
                                renderWeekChart(res.data.xData, res.data.newAddData, res.data.activeData);
                            }, 0);
                            break;
                        case 3:
                            monthActiveFetchFlag.value = true;
                            setTimeout(() => {
                                renderMonthChart(res.data.xData, res.data.newAddData, res.data.activeData);
                            }, 0);
                            break;
                        default:
                            break;
                    }
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            mainStore.dealResponseErrInfo(-1);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function dayTimeRadioChange(val: any) {
    switch (val) {
        case 1:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(1);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(1);
            break;
        case 2:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(2);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 3:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(3);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        case 4:
            dayFilterForm.date_range[0] = mainStore.quckGetDate(4);
            dayFilterForm.date_range[1] = mainStore.quckGetDate(2);
            break;
        default:
            break;
    }
}

function renderDayChart(xData: Array<any>, data1: Array<number>, data2: Array<number>) {
    const dayActiveCanvas = document.getElementById('dayUserActiveCanvas');
    if (dayActiveCanvas) {
        const dayActiveChart = echarts.init(dayActiveCanvas);
        const option = {
            title: {
                text: '新增、活跃数据'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {},
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: [
                {
                    type: 'category',
                    data: xData
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: [
                {
                    name: '新增',
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: data1
                },
                {
                    name: '活跃',
                    type: 'bar',
                    stack: 'Ad',
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: data2
                },
            ]
        };
        option && dayActiveChart.setOption(option);
    }
}

function renderWeekChart(xData: Array<any>, data1: Array<number>, data2: Array<number>) {
    const weekActiveCanvas = document.getElementById('weekUserActiveCanvas');
    if (weekActiveCanvas) {
        const weekActiveChart = echarts.init(weekActiveCanvas);
        const option = {
            title: {
                text: '周新增、活跃数据'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {},
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: [
                {
                    type: 'category',
                    data: xData
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: [
                {
                    name: '新增',
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: data1
                },
                {
                    name: '活跃',
                    type: 'bar',
                    stack: 'Ad',
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: data2
                },
            ]
        };
        option && weekActiveChart.setOption(option);
    }
}

function renderMonthChart(xData: Array<any>, data1: Array<number>, data2: Array<number>) {
    const monthActiveCanvas = document.getElementById('monthUserActiveCanvas');
    if (monthActiveCanvas) {
        const monthActiveChart = echarts.init(monthActiveCanvas);
        const option = {
            title: {
                text: '月新增、活跃数据'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {},
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {},
                    dataView: {
                        show: true
                    }
                }
            },
            xAxis: [
                {
                    type: 'category',
                    data: xData
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: [
                {
                    name: '新增',
                    type: 'bar',
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: data1
                },
                {
                    name: '活跃',
                    type: 'bar',
                    stack: 'Ad',
                    emphasis: {
                        focus: 'series'
                    },
                    label: {
                        show: true,
                        position: 'outside'
                    },
                    data: data2
                },
            ]
        };
        option && monthActiveChart.setOption(option);
    }
}

</script>

<template>
    <div class="user-detai" v-if="mainStore.checkPermission(4101)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'statistics'
                }">
                    数据统计
                </el-breadcrumb-item>
                <el-breadcrumb-item>用户新增&活跃</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill :size="32">
                    <el-tabs type="border-card" v-model="tabIndex">
                        <el-tab-pane label="日活" name="day">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="dayFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="时间" size="large">
                                                <el-col :span="7">
                                                    <el-date-picker v-model="dayFilterForm.date_range" type="daterange"
                                                        start-placeholder="开始日期" end-placeholder="结束日期" size="large"
                                                        value-format="YYYY-MM-DD"
                                                        :default-value="[new Date(), new Date()]" />
                                                </el-col>
                                                <el-col :span="17">
                                                    <el-radio-group v-model="dayFilterForm.quick_select" size="large"
                                                        @change="dayTimeRadioChange">
                                                        <el-radio :label="1" size="large">今日</el-radio>
                                                        <el-radio :label="2" size="large">昨日</el-radio>
                                                        <el-radio :label="3" size="large">最近7天</el-radio>
                                                        <el-radio :label="4" size="large">最近30天</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-form-item>
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="dayFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click="statisticsUserActiveData(1)"
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>
                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="dayUserActiveCanvas" style="height: 400px;" v-if="dayActiveFetchFlag"></div>
                            </el-space>
                        </el-tab-pane>
                        <el-tab-pane label="周活" name="week">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="dayFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <!-- <el-form-item label="时间" size="large">
                                                <el-col :span="7">
                                                    <el-date-picker v-model="dayFilterForm.date_range" type="daterange"
                                                        start-placeholder="开始日期" end-placeholder="结束日期" size="large"
                                                        value-format="YYYY-MM-DD"
                                                        :default-value="[new Date(), new Date()]" />
                                                </el-col>
                                                <el-col :span="17">
                                                    <el-radio-group v-model="dayFilterForm.quick_select" size="large"
                                                        @change="dayTimeRadioChange">
                                                        <el-radio :label="1" size="large">今日</el-radio>
                                                        <el-radio :label="2" size="large">昨日</el-radio>
                                                        <el-radio :label="3" size="large">最近7天</el-radio>
                                                        <el-radio :label="4" size="large">最近30天</el-radio>
                                                    </el-radio-group>
                                                </el-col>
                                            </el-form-item> -->
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="dayFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>
                                                <el-col :span="4">
                                                    <el-button type="primary" @click="statisticsUserActiveData(2)"
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>
                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="weekUserActiveCanvas" style="height: 400px;" v-if="weekActiveFetchFlag"></div>
                            </el-space>

                        </el-tab-pane>
                        <el-tab-pane label="月活" name="month">
                            <el-space direction="vertical" style="width: 100%" fill :size="32">
                                <el-row>
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-form :model="dayFilterForm">
                                            <!-- <el-form-item label="平台" size="large">
                                                <el-radio-group v-model="dayFilterForm.platform" size="large">
                                                    <el-radio :label="-1" size="large">全部</el-radio>
                                                    <el-radio :label="1" size="large">PC</el-radio>
                                                    <el-radio :label="2" size="large">Android</el-radio>
                                                    <el-radio :label="3" size="large">IOS</el-radio>
                                                </el-radio-group>
                                            </el-form-item> -->
                                            <el-form-item label="渠道" size="large">
                                                <el-col :span="4">
                                                    <el-select v-model="dayFilterForm.app_channel" placeholder="选择渠道"
                                                        size="large">
                                                        <el-option v-for="item in mainStore.channelList"
                                                            :key="item.channel_id" :label="item.channel_name"
                                                            :value="item.channel_id" />
                                                    </el-select>
                                                </el-col>

                                                <el-col :span="4">
                                                    <el-button type="primary" @click="statisticsUserActiveData(3)"
                                                        size="large">查询
                                                    </el-button>
                                                </el-col>
                                            </el-form-item>

                                        </el-form>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                                <div id="monthUserActiveCanvas" style="height: 400px;" v-if="monthActiveFetchFlag">
                                </div>
                            </el-space>
                        </el-tab-pane>
                    </el-tabs>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped>

</style>

