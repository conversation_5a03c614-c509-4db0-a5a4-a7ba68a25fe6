import { defineStore } from "pinia";
import { useMainStore } from "./index";
import { router } from "../router";
import {
  AvatarReview,
  GameItem,
  GameSeries,
  NicknameReview,
  ReportItem,
  UserActivityLotteryRecord,
  UserActivityTaskRecord,
  UserLotteryRecord,
  UserTaskRecord,
} from "../interface";

let fullLoading: any;

export const useActivityStore = defineStore("activity", {
  state: () => ({
    usersTaskTotal: 0,
    usersTaskUserNum: 0,
    usersTaskList: [] as Array<UserTaskRecord>,
    lotteryTotal: 0,
    lotteryUserNum: 0,
    lotteryRecord: [] as Array<UserLotteryRecord>,
    specialActivityList: [
      {
        activity_id: 6,
        name: "2022中秋活动",
      },
      {
        activity_id: 7,
        name: "2022金币作战",
      },
      {
        activity_id: 10,
        name: "2022万圣节活动",
      },
      {
        activity_id: 22,
        name: "2022配色抽奖活动",
      },
      {
        activity_id: 25,
        name: "荣耀水晶夺宝活动",
      },
      {
        activity_id: 26,
        name: "2023春节活动",
      },
      {
        activity_id: 27,
        name: "2023元宵节活动",
      },
      {
        activity_id: 30,
        name: "至尊水晶夺宝活动",
      },
    ],
    usersActivityTaskTotal: 0,
    activityTaskUserNum: 0,
    usersActivityTaskRecord: [] as Array<UserActivityTaskRecord>,
    usersActivityLotteryTotal: 0,
    activityLotteryUserNum: 0,
    usersActivityLotteryRecord: [] as Array<UserActivityLotteryRecord>,
  }),
  getters: {},
  actions: {},
});
