<script setup lang="ts">
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { useRoute } from 'vue-router';
import { router } from '../../router';
import { Warning, SwitchButton, User, Menu } from '@element-plus/icons-vue';
import crypto from 'crypto-js';
import requset from '../../api';

const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();


function menuGoRoute(name: string) {
    if (route.name === name) {
        return;
    }
    router.push({
        name: name
    });
}


</script>

<template>
    <el-aside class="admin-aside">
        <el-menu class="admin-menu" :default-active="route.name" text-color="#fff" background-color="#333744" @open=""
            @close="" v-if="mainStore.queryMenuPermission(mainStore.menuList, 5000)">
            <el-sub-menu index="1">
                <template #title>
                    <el-icon>
                        <Warning />
                    </el-icon>
                    <span>活动管理</span>
                </template>
                <el-menu-item index="taskCount" @click="menuGoRoute('taskCount')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 5001)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>每日任务</span>
                </el-menu-item>
                <el-menu-item index="lotteryCount" @click="menuGoRoute('lotteryCount')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 5002)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>金币夺宝</span>
                </el-menu-item>
                <el-menu-item index="specialTask" @click="menuGoRoute('specialTask')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 5003)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>活动任务</span>
                </el-menu-item>
                <el-menu-item index="specialLottery" @click="menuGoRoute('specialLottery')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 5004)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>活动抽奖</span>
                </el-menu-item>
            </el-sub-menu>
        </el-menu>
    </el-aside>
    <el-main class="admin-content">
        <router-view v-if="mainStore.checkPermission(5000)"></router-view>
        <h2 v-else>权限不足, 请联系管理员!</h2>
    </el-main>
</template>

<style lang="less" scoped>

</style>