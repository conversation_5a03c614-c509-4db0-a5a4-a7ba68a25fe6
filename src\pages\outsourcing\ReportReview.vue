<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import { NicknameReview, GameItem, ReportItem, UserAvatar } from '../../interface';
import type { FormInstance, FormRules } from 'element-plus';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const handleReportDialog = ref(false);
const reportTableRef = ref();

const filterForm = reactive({
    game_series: '',
    game_id: [] as Array<Number>,
    report_type: 0x00
});

const reportTypeList = [
    {
        type_id: 0x00,
        type_name: '全部'
    },
    {
        type_id: 0x01,
        type_name: '恶意挂机'
    },
    {
        type_id: 0x02,
        type_name: '刷等级'
    },
    {
        type_id: 0x04,
        type_name: '违规言论'
    },
    {
        type_id: 0x08,
        type_name: '广告'
    },
    {
        type_id: 0x10,
        type_name: '使用外挂'
    },
    {
        type_id: 0x40,
        type_name: '违规头像'
    },
    {
        type_id: 0x80,
        type_name: '违规昵称'
    },
    {
        type_id: 0x20,
        type_name: '其它'
    },
];

const reoportPunishType = [
    {
        id: 1,
        label: '禁止发言'
    },
    {
        id: 2,
        label: '封禁帐户'
    }
];
const reoportPunishTime = [
    {
        id: 1,
        label: '1天'
    },
    {
        id: 2,
        label: '3天'
    },
    {
        id: 3,
        label: '7天'
    },
    {
        id: 4,
        label: '15天'
    },
    {
        id: 5,
        label: '30天'
    },
    {
        id: 6,
        label: '90天'
    },
    {
        id: 7,
        label: '180天'
    },
    {
        id: 8,
        label: '365天'
    },
    {
        id: 9,
        label: '3650天'
    },
];
const handleReportType = [
    {
        type_id: 1,
        type_name: '无问题',
        conclusion: '经官方核实, 没有发现违规行为, 感谢您的反馈!'
    },
    {
        type_id: 2,
        type_name: '使用外挂/非法第三方辅助程序/软硬件连发等原因',
        conclusion: '经官方核实, 情况属实, 已给予封号处理, 感谢您的举报!',
        default_punish_type: 2,
        default_punish_time: 9

    },
    {
        type_id: 3,
        type_name: '清除战绩',
        conclusion: '经官方核实, 情况属实, 已给予清除战绩处理, 感谢您的举报!',
        default_punish_type: 2,
        default_punish_time: 2
    },
    {
        type_id: 4,
        type_name: '恶意举报',
        conclusion: '请不要恶意举报捣乱, 感谢您的配合!'
    },
    {
        type_id: 5,
        type_name: '违规昵称',
        conclusion: '经官方核实, 情况属实, 已清除违规昵称, 感谢您的举报!'
    },
    {
        type_id: 6,
        type_name: '违规头像',
        conclusion: '经官方核实, 情况属实, 已清除违规头像, 感谢您的举报!'
    },
    {
        type_id: 7,
        type_name: '违规发言',
        conclusion: '经官方核实, 情况属实, 已对其进行禁言处理, 感谢您的举报!',
        default_punish_type: 1,
        default_punish_time: 1
    },
    {
        type_id: 8,
        type_name: '已处理',
        conclusion: '经官方核实, 情况属实, 已对其进行处理, 感谢您的举报!',
    },
];


const filterFormRules = [
    {
        
    }
];


const currentReportTask = reactive({
    id: 0,
    bereport_userid: 0,
    handle_type: '',
    conclusion: '',
    all_flag: true,
    punish_type: 0,
    punish_time: 0,
    avatar_list: [] as Array<UserAvatar>
});
const handleReportFormRef = ref<FormInstance>();
const handleReportFormRules = reactive<FormRules>({
    conclusion: [
        {
            required: true, message: '请输入反馈内容', trigger: 'blur'
        }
    ],
    handle_type: [
        {
            required: true, message: '请选择处理类型', trigger: 'blur'
        }
    ],
    all_flag: [
        {
            required: true, message: '是否处理相同被举报人', trigger: 'blur'
        }
    ],
    punish_result: [
        {
            required: true, message: '请选择处罚结果', trigger: 'blur'
        }
    ],
});


function selectGameSeries(val: any) {
    if (val === -1) {
        filterForm.game_id = mainStore.gameList.map(item => item.game_id);
        outsourcingStore.reportGameListOptions = mainStore.gameList.map(item => ({
            value: item.game_id,
            label: item.game_name
        }));
    } else {
        filterForm.game_id = mainStore.gameList.filter(item => item.series_id === val).map(item => item.game_id);
        outsourcingStore.reportGameListOptions = mainStore.gameList.filter(item => item.series_id === val).map(item => ({
            value: item.game_id,
            label: item.game_name
        }));
    }
}

function selectGame(val: any) {
    if (val === -1) {
        filterForm.game_id = outsourcingStore.reportGameListOptions.map(item => item.value);
    }
}

function clearGameSeries() {
    outsourcingStore.reportGameListOptions = mainStore.gameList.map(item => ({
        value: item.game_id,
        label: item.game_name
    }));
}

async function getReportTask() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const gameID = filterForm.game_id.join(',');
    const reportType = filterForm.report_type;
    const token = crypto.SHA1(`${userID}${gameID}${reportType}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getReportList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            gameID: gameID,
            reportType: reportType,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.reportList) {
                    outsourcingStore.reportReviewList = res.data.reportList;
                    outsourcingStore.reportReviewNum = res.data.total;
                    outsourcingStore.reportReviewProcessed = res.data.processed;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        }).finally(() => {
            loading.value = false;
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function getReportType(type: number) {
    const reportType = [] as Array<String>;
    reportTypeList.forEach(item => {
        if (item.type_id > 0) {
            if ((item.type_id & type) === item.type_id) {
                reportType.push(item.type_name);
            }
        }
    });
    return reportType.join(',');
}

async function cancelReportTask(reportID: number) {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${reportID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.cancelReportTask({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            reportID: reportID,
            token: token
        }).then(res => {
            if (res.code === 0) {
                outsourcingStore.removeReportTask(reportID);
                if (res.data) {
                    outsourcingStore.reportReviewNum = res.data.total;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        }).finally(() => {
            loading.value = false;
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function handleReport(item: ReportItem) {
    currentReportTask.id = item.id;
    currentReportTask.bereport_userid = item.bereport_userid;
    currentReportTask.handle_type = '';
    currentReportTask.avatar_list = [];
    currentReportTask.punish_type = 0;
    currentReportTask.punish_time = 0;
    currentReportTask.conclusion = '';
    handleReportDialog.value = true;
}

async function handleReportSubmit(formEl: FormInstance | undefined) {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${currentReportTask.id}${currentReportTask.handle_type}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.reportHandle({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            reportID: currentReportTask.id,
            handleType: currentReportTask.handle_type,
            punishType: currentReportTask.punish_type,
            punishTime: currentReportTask.punish_time,
            conclusion: currentReportTask.conclusion,
            allFlag: currentReportTask.all_flag ? 1 : 0,
            token: token
        }).then(res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
                if (currentReportTask.all_flag === true) {
                    outsourcingStore.removeCommonReport(currentReportTask.bereport_userid);
                } else {
                    outsourcingStore.removeReportTask(currentReportTask.id);
                }
                if (outsourcingStore.reportReviewList.length === 0) {
                    getReportTask();
                }

            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该用户已被处理', 3);
                            outsourcingStore.removeReportTask(currentReportTask.id);
                            break;
                        case 6:
                            mainStore.globalMessageTip('该用户已不存在', 3);
                            outsourcingStore.removeReportTask(currentReportTask.id);
                            break;
                        default:
                            break;
                    }
                }
            }
            handleReportDialog.value = false;
        }).catch(err => {
            console.log(err);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function selectReportHandleType(val: any) {
    if (val === 6) {
        getTargetUserAvatarList();
    }
    handleReportType.some(item => {
        if (item.type_id === val) {
            currentReportTask.conclusion = item.conclusion;
            if (item.default_punish_type) {
                currentReportTask.punish_type = item.default_punish_type;
            } else {
                currentReportTask.punish_type = 0;
            }
            if (item.default_punish_time) {
                currentReportTask.punish_time = item.default_punish_time;
            } else {
                currentReportTask.punish_time = 0;
            }
            return true;
        }
    });
}

function getTargetUserAvatarList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const targetUserId = currentReportTask.bereport_userid;
    const token = crypto.SHA1(`${userID}${authorityID}${targetUserId}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        requset.getTargetUserAvatarList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetUserID: targetUserId,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.avatarList) {
                    currentReportTask.avatar_list = res.data.avatarList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function reportTableRowClick(row: any, column: any, event: any) {
    reportTableRef.value.toggleRowExpansion(row);
}

</script>

<template>
    <div class="report-review" v-if="mainStore.checkPermission(1101)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'outsourcing'
                }">
                    外协管理
                </el-breadcrumb-item>
                <el-breadcrumb-item>举报处理</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>待处理任务条数：{{ outsourcingStore.reportReviewNum }}</span> <span>今日已处理：{{
                                    outsourcingStore.reportReviewProcessed
                                }}</span>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-form ref="filterFormRef" :model="filterForm" status-icon :rules="filterFormRules"
                        label-width="120px" class="filter-form">
                        <el-form-item label="举报游戏">
                            <el-space wrap>
                                <el-select-v2 v-model="filterForm.game_series" clearable
                                    :options="outsourcingStore.reportGameSeriesOptions" placeholder="选择游戏分类"
                                    @change="selectGameSeries" @clear="clearGameSeries" size="large" />
                                <el-select-v2 v-model="filterForm.game_id" clearable filterable multiple collapse-tags
                                    collapse-tags-tooltip :options="outsourcingStore.reportGameListOptions"
                                    style="width: 240px" placeholder="选择游戏名称" @change="selectGame" size="large" />
                            </el-space>
                        </el-form-item>
                        <el-form-item label="举报类型">
                            <el-radio-group v-model="filterForm.report_type">
                                <el-radio :label="item.type_id" v-for="(item, index) in reportTypeList"
                                    :key="item.type_id">{{ item.type_name }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="">
                            <el-button type="primary" @click="getReportTask()" size="large">获取任务
                            </el-button>
                        </el-form-item>
                    </el-form>
                    <el-table :data="outsourcingStore.reportReviewList" border style="width: 100%" toggleRowExpansion
                        ref="reportTableRef" @row-click="reportTableRowClick">
                        <el-table-column type="expand"><template #default="props">
                                <el-row :style="`font-size: var(--el-font-size-medium)`">
                                    <el-col :span="1"></el-col>
                                    <el-col :span="22">
                                        <el-space size="large" direction="vertical" style="width: 100%;" fill>
                                            <el-row>
                                                <el-col :span="2">任务编号: </el-col>
                                                <el-col :span="22">{{ props.row.id }}</el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">举报时间: </el-col>
                                                <el-col :span="22">{{ props.row.report_time }}</el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">游戏: </el-col>
                                                <el-col :span="22">{{ mainStore.getGameName(props.row.game_id) }}
                                                </el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="2">举报人: </el-col>
                                                <el-col :span="22">
                                                    <el-row align="middle">
                                                        <el-col :span="3">
                                                            <el-link type="primary" :underline="false"
                                                                @click="mainStore.copyContent(props.row.nickname)">{{
                                                                    props.row.nickname
                                                                }}</el-link>

                                                        </el-col>
                                                        <el-col :span="2">
                                                            (ID: {{ props.row.user_id }})
                                                        </el-col>
                                                        <el-col :span="2">
                                                            <el-link type="success" :underline="false"
                                                                @click="mainStore.gotoUserDetail(props.row.user_id)">
                                                                查看玩家详情</el-link>

                                                        </el-col>
                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                            <el-row align="middle">
                                                <el-col :span="2">被举报人: </el-col>
                                                <el-col :span="22">
                                                    <el-row align="middle">
                                                        <el-col :span="3">
                                                            <el-link type="primary" :underline="false"
                                                                @click="mainStore.copyContent(props.row.bereport_nickname)">
                                                                {{
                                                                    props.row.bereport_nickname
                                                                }}</el-link>

                                                        </el-col>
                                                        <el-col :span="2">
                                                            (ID: {{ props.row.bereport_userid }})
                                                        </el-col>
                                                        <el-col :span="2">
                                                            <el-link type="success" :underline="false"
                                                                @click="mainStore.gotoUserDetail(props.row.bereport_userid)">
                                                                查看玩家详情</el-link>

                                                        </el-col>
                                                    </el-row>
                                                </el-col>
                                            </el-row>
                                            <el-row>
                                                <el-col :span="2">举报描述: </el-col>
                                                <el-col :span="22">{{ props.row.report_desc }}</el-col>
                                            </el-row>
                                            <el-row v-if="props.row.talk_list">
                                                <el-col :span="2">聊天记录: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="props.row.talk_list" style="width: 100%" border>
                                                        <el-table-column prop="talk_time" label="聊天时间" />
                                                        <el-table-column label="游戏名称">
                                                            <template #default="props">
                                                                <span>{{ mainStore.getGameName(props.row.game_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="服务器">
                                                            <template #default="props">
                                                                <span>{{
                                                                    mainStore.getGameServerName(props.row.game_id,
                                                                        props.row.server_id)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="发言位置">
                                                            <template #default="props">
                                                                <span>{{ mainStore.getRoomName(props.row.room_id) }}
                                                                </span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="msg" label="聊天内容" />
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row v-if="props.row.hack_list">
                                                <el-col :span="2">外挂检测: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="props.row.hack_list" style="width: 100%" border>
                                                        <el-table-column prop="id" label="序号" />
                                                        <el-table-column prop="check_time" label="检测时间">
                                                        </el-table-column>
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row v-if="props.row.vs_match_record">
                                                <el-col :span="2">对战记录: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="props.row.vs_match_record" style="width: 100%" border>
                                                        <el-table-column prop="id" label="ID" />
                                                        <el-table-column label="p1_id">
                                                            <template #default="props">
                                                                <el-link type="primary" :underline="false"
                                                                    @click="mainStore.gotoUserDetail(props.row.p1_userid)">
                                                                    {{
                                                                        props.row.p1_userid
                                                                    }}</el-link>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column label="p2_id">
                                                            <template #default="props">
                                                                <el-link type="primary" :underline="false"
                                                                    @click="mainStore.gotoUserDetail(props.row.p2_userid)">
                                                                    {{
                                                                        props.row.p2_userid
                                                                    }}</el-link>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="p1_ip" label="p1_ip" />
                                                        <el-table-column prop="p2_ip" label="p2_ip" />
                                                        <el-table-column prop="p1_level" label="p1等级" />
                                                        <el-table-column prop="p2_level" label="p2等级" />
                                                        <el-table-column prop="p1_win_num" label="p1胜场" />
                                                        <el-table-column prop="p2_win_num" label="p2胜场" />
                                                        <el-table-column label="结果">
                                                            <template #default="props">
                                                                <span>{{
                                                                    mainStore.getVsResult(props.row.vs_result)
                                                                }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="match_time" label="时间" />

                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                            <el-row v-if="props.row.coop_match_record">
                                                <el-col :span="2">闯关记录: </el-col>
                                                <el-col :span="22">
                                                    <el-table :data="props.row.coop_match_record" style="width: 100%"
                                                        border>
                                                        <el-table-column prop="id" label="ID" />
                                                        <el-table-column label="结果">
                                                            <template #default="props">
                                                                <span>{{ mainStore.getCoopResult(props.row.clear) }}</span>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="coin" label="投币数" />
                                                        <el-table-column prop="score" label="分数" />
                                                        <el-table-column prop="match_time" label="时间" />
                                                    </el-table>
                                                </el-col>
                                            </el-row>
                                        </el-space>
                                    </el-col>
                                    <el-col :span="1"></el-col>
                                </el-row>
                            </template></el-table-column>
                        <el-table-column prop="report_time" label="举报时间" />
                        <el-table-column prop="game_name" label="游戏">
                            <template #default="props">
                                <span>{{ mainStore.getGameName(props.row.game_id) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="nickname" label="举报人" />
                        <el-table-column prop="bereport_nickname" label="被举报人" />
                        <el-table-column label="举报类型">
                            <template #default="props">
                                <span>{{ getReportType(props.row.report_type) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-button type="danger" @click.stop="handleReport(scope.row)" size="large">处理
                                </el-button>
                                <el-button type="info" @click.stop="cancelReportTask(scope.row.id)" size="large">放弃
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>

    <el-dialog v-model="handleReportDialog" :title="'处理第' + currentReportTask.id + '号任务'">
        <el-form :model="currentReportTask" ref="handleReportFormRef" status-icon :rules="handleReportFormRules">
            <el-form-item label="处理结果" prop="handle_type">
                <el-select v-model="currentReportTask.handle_type" placeholder="请选择" style="width: 50%;"
                    @change="selectReportHandleType" size="large">
                    <el-option v-for="(item, index) in handleReportType" :key="item.type_id" :label="item.type_name"
                        :value="item.type_id" />
                </el-select>
            </el-form-item>
            <el-form-item label="处罚结果" prop="punish_result"
                v-if="currentReportTask.handle_type !== '' && currentReportTask.punish_type > 0 && currentReportTask.punish_time > 0">
                <el-space wrap>
                    <el-select v-model="currentReportTask.punish_type" placeholder="请选择处罚类型" style="width: 25%;"
                        size="large">
                        <el-option v-for="(item, index) in reoportPunishType" :key="item.id" :label="item.label"
                            :value="item.id" />
                    </el-select>
                    <el-select v-model="currentReportTask.punish_time" placeholder="请选择处罚时间" style="width: 25%;"
                        size="large">
                        <el-option v-for="(item, index) in reoportPunishTime" :key="item.id" :label="item.label"
                            :value="item.id" />
                    </el-select>
                </el-space>
            </el-form-item>
            <el-form-item label="头像列表" v-if="currentReportTask.handle_type == '6'" required>
                <el-row style="width: 50%;">
                    <el-space wrap>
                        <el-avatar :src="item.icon_url" v-for="(item, index) in currentReportTask.avatar_list"
                            :key="item.id" />
                    </el-space>
                </el-row>

            </el-form-item>
            <el-form-item label="处理反馈" prop="conclusion" v-if="currentReportTask.handle_type !== ''">
                <el-input v-model="currentReportTask.conclusion" placeholder="输入处理反馈" style="width: 50%;" size="large" />
            </el-form-item>
            <el-form-item label="处理相同被举报人" prop="all_flag" v-if="currentReportTask.handle_type !== ''">
                <el-switch v-model="currentReportTask.all_flag" size="large" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="handleReportSubmit(handleReportFormRef)" size="large">确定</el-button>
                <el-button @click="handleReportDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped></style>