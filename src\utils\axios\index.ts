import axios from "axios";
import qs from "qs";
declare module "axios" {
  interface AxiosInstance {
    (config: AxiosRequestConfig): Promise<any>;
  }
}

export const API_ROOT = "http://127.0.0.1/gm_admin/";
// export const API_ROOT = "http://114.215.198.121/api/yzkofvs/admin/";
// export const API_ROOT = "https://www.xugameplay.com/api/yzkofvs/admin/";

export const service = axios.create({
  baseURL: API_ROOT,
});

//axios请求拦截
service.interceptors.request.use((config) => {
  if (config.method === "post") {
    config.data = qs.stringify(config.data);
  }
  return config;
});
//axios响应拦截
service.interceptors.response.use((res) => {
  if (res.data) {
    console.log(res.data);
    return res.data;
  }
});
