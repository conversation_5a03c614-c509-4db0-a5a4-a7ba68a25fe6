<script setup lang="ts">

</script>

<template>
  <Suspense>
    <router-view></router-view>
  </Suspense>
</template>

<style lang="less">
html,
body,
#app {
  width: 100%;
  height: 100%;
  font-size: 16px;
}

.admin-aside {
  width: 200px;
  background-color: #333744;
}

.admin-header {
  .header-menu {
    border-bottom: none;
  }
}

.admin-menu {
  background-color: #333744;
  border-right: none;

  .el-sub-menu {

    .el-sub-menu__title {
      position: relative;

      .el-tag {
        position: absolute;
        right: 20%;
      }
    }

    .el-menu-item {
      position: relative;
      background-color: #333744;

      &:hover {
        background-color: #292c36;
      }

      &.is-active {
        color: #409eff;
      }

      .el-tag {
        position: absolute;
        right: 5%;
      }
    }
  }



  .el-sub-menu__title {
    background-color: #333744;

    &:hover {
      background-color: #292c36;
    }
  }
}

.el-select-v2__popper {
  .el-select-dropdown {
    .el-vl__wrapper {
      .el-virtual-scrollbar {
        display: none;
      }
    }
  }
}

.el-overlay-dialog {
  .el-dialog__footer {
    text-align: center;
  }
}

.el-table,
.el-form-item,
.el-link,
.el-tag,
.el-menu-item,
.el-select,
.el-input,
.el-select-dropdown__item,
.el-breadcrumb,
.el-button--large,
.el-form-item__label,
.el-message-box,
.el-message-box__content,
.el-radio__label,
.el-select-dropdown__option-item,
.el-select-v2,
.el-select-v2--large .el-select-v2__placeholder,
.el-radio.el-radio--large .el-radio__label,
.el-range-editor--large .el-range-input,
.el-input--large {
  font-size: 16px;
}
</style>
