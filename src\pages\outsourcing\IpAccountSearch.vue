<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { router } from '../../router';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const currentPage = ref(1);
const banUserDialog = ref(false);
const unfreezeUserDialog = ref(false);

const filterForm = reactive({
    nickname: '',
    account: '',
    user_id: '',
    ip: '',
    find_type: 1
});

const reoportPunishType = [
    {
        id: 2,
        label: '封禁帐户'
    },
    {
        id: 1,
        label: '禁止发言'
    },
];
const unfreezeType = [
    {
        id: 2,
        label: '解除封号'
    },
    {
        id: 1,
        label: '解除禁言'
    },
];
const reoportPunishTime = [
    {
        id: 1,
        label: '1天'
    },
    {
        id: 2,
        label: '3天'
    },
    {
        id: 3,
        label: '7天'
    },
    {
        id: 4,
        label: '15天'
    },
    {
        id: 5,
        label: '30天'
    },
    {
        id: 6,
        label: '90天'
    },
    {
        id: 7,
        label: '180天'
    },
    {
        id: 8,
        label: '365天'
    },
    {
        id: 9,
        label: '3650天'
    },
];
const handleReportType = [
    {
        type_id: 2,
        type_name: '外挂或连发',
    },
    {
        type_id: 3,
        type_name: '恶意刷级',
    },
    {
        type_id: 7,
        type_name: '违规言论',
    },
    {
        type_id: 8,
        type_name: '其它',
    },
];

const handleUserForm = reactive({
    target_userid: 0,
    handle_type: 2,
    punish_type: 2,
    punish_time: 1,
});
const unfreezeUserForm = reactive({
    target_userid: 0,
    unfreeze_type: 2,
});

const handleReportFormRef = ref<FormInstance>();
const handleReportFormRules = reactive<FormRules>({
    punish_type: [
        {
            required: true, message: '请选择处罚类型', trigger: 'blur'
        }
    ],
    punish_time: [
        {
            required: true, message: '请选择封禁时间', trigger: 'blur'
        }
    ],
    handle_type: [
        {
            required: true, message: '请选择封禁原因', trigger: 'blur'
        }
    ],
});
const unfreezeFormRef = ref<FormInstance>();
const unfreezeFormRules = reactive<FormRules>({
    unfreeze_type: [
        {
            required: true, message: '请选择处罚类型', trigger: 'blur'
        }
    ],
});

async function searchUsers() {

    if (!filterForm.ip) {
        return mainStore.globalMessageTip('请输入IP', 1);
    }

    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${filterForm.ip}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;

        await requset.getAccountListWithIp({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            ip: filterForm.ip,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    outsourcingStore.searchIpUsers = res.data.accountList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function currentPageChange(page: any) {
    currentPage.value = page;
    await searchUsers();
}

function banUser(user_id: number) {
    handleUserForm.target_userid = user_id;
    banUserDialog.value = true;
}

async function banUserSubmit() {
    if (!handleUserForm.target_userid) {
        banUserDialog.value = false;
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${handleUserForm.target_userid}${handleUserForm.handle_type}${handleUserForm.punish_type}${handleUserForm.punish_time}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.banUser({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_userid: handleUserForm.target_userid,
            handleType: handleUserForm.handle_type,
            punishType: handleUserForm.punish_type,
            punishTime: handleUserForm.punish_time,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    mainStore.globalMessageTip('处理成功', 0);
                    outsourcingStore.searchIpUsers.some(item => {
                        if (item.user_id === handleUserForm.target_userid) {
                            item.account_state = res.data.account_state;
                            return true;
                        }
                    });
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        banUserDialog.value = false;
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function unfreezeUser(user_id: number) {
    unfreezeUserForm.target_userid = user_id;
    unfreezeUserDialog.value = true;
}

async function unfreezeUserSubmit() {
    if (!unfreezeUserForm.target_userid) {
        unfreezeUserDialog.value = false;
        return;
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${unfreezeUserForm.target_userid}${unfreezeUserForm.unfreeze_type}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.unfreezeUser({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_userid: unfreezeUserForm.target_userid,
            unfreezeType: unfreezeUserForm.unfreeze_type,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data) {
                    mainStore.globalMessageTip('处理成功', 0);
                    outsourcingStore.searchIpUsers.some(item => {
                        if (item.user_id === unfreezeUserForm.target_userid) {
                            item.account_state = res.data.account_state;
                            return true;
                        }
                    });
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        unfreezeUserDialog.value = false;
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

</script>

<template>
    <div class="account-search" v-if="mainStore.checkPermission(1004)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'outsourcing'
                }">
                    外协管理
                </el-breadcrumb-item>
                <el-breadcrumb-item>IP查询</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 50%" fill>
                    <el-form ref="ruleFormRef" :model="filterForm" status-icon class="login-form">
                        <el-form-item prop="account" label="IP" label-width="120px">
                            <el-input placeholder="请输入IP" v-model="filterForm.ip" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item prop="account" label-width="120px">
                            <el-button type="primary" size="large" @click="searchUsers()">确定查找</el-button>
                        </el-form-item>
                    </el-form>
                </el-space>
                <el-table :data="outsourcingStore.searchIpUsers" border style="width: 100%">
                    <!-- <el-table-column type="selectio"></el-table-column> -->
                    <el-table-column prop="user_id" label="ID" />
                    <el-table-column prop="account" label="账号" />
                    <el-table-column label="昵称">
                        <template #default="scope">
                            <el-link type="primary" :underline="false"
                                @click="mainStore.gotoUserDetail(scope.row.user_id)">{{ scope.row.nickname }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="last_login_time" label="最后登录时间" />
                    <!-- <el-table-column prop="register_time" label="注册时间" /> -->
                    <el-table-column prop="register_ip" label="注册IP" />
                    <el-table-column prop="ip_last_login" label="IP登录时间">
                    </el-table-column>
                    <!-- <el-table-column prop="last_recharge_time" label="最近充值时间">
                    </el-table-column> -->
                    <el-table-column prop="vip_level" label="VIP等级">
                    </el-table-column>
                    <el-table-column prop="account_state" label="账号状态">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="danger" @click="banUser(scope.row.user_id)">处罚
                            </el-button>
                            <el-button type="info" @click="unfreezeUser(scope.row.user_id)">解禁
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- <el-pagination background layout="prev, pager, next" :total="outsourcingStore.searchUserTotal"
                    :current-page="currentPage" @current-change="currentPageChange" /> -->

            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="banUserDialog" title="封禁">
        <el-form :model="handleUserForm" ref="handleReportFormRef" status-icon :rules="handleReportFormRules">
            <el-form-item label="类型" prop="punish_type">
                <el-select v-model="handleUserForm.punish_type" placeholder="请选择处罚类型" style="width: 50%;" size="large">
                    <el-option v-for="(item, index) in reoportPunishType" :key="item.id" :label="item.label"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="天数" prop="punish_time">
                <el-select v-model="handleUserForm.punish_time" placeholder="请选择" style="width: 50%;" size="large">
                    <el-option v-for="(item, index) in reoportPunishTime" :key="item.id" :label="item.label"
                        :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="原因" prop="handle_type" v-if="handleUserForm.punish_type === 2">
                <el-select v-model="handleUserForm.handle_type" placeholder="请选择" style="width: 50%;" size="large">
                    <el-option v-for="(item, index) in handleReportType" :key="item.type_id" :label="item.type_name"
                        :value="item.type_id" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="banUserSubmit()" size="large">确定</el-button>
                <el-button @click="banUserDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="unfreezeUserDialog" title="解除封禁">
        <el-form :model="unfreezeUserForm" ref="unfreezeFormRef" status-icon :rules="unfreezeFormRules">
            <el-form-item label="类型" prop="unfreeze_type">
                <el-select v-model="unfreezeUserForm.unfreeze_type" placeholder="请选择解除类型" style="width: 50%;">
                    <el-option v-for="(item, index) in unfreezeType" :key="item.id" :label="item.label"
                        :value="item.id" />
                </el-select>
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="unfreezeUserSubmit()">确定</el-button>
                <el-button @click="unfreezeUserDialog = false">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped>

</style>