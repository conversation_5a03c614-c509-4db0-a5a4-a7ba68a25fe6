import { createRouter, createWebHashHistory } from "vue-router";
import Login from "../pages/Login.vue";
import Home from "../pages/Home.vue";
import Welcome from "../pages/Welcome.vue";
import Outsourcing from "../pages/outsourcing/Outsourcing.vue";
import NicknameReview from "../pages/outsourcing/NicknameReview.vue";
import AvatarReview from "../pages/outsourcing/AvatarReview.vue";
import ReportReview from "../pages/outsourcing/ReportReview.vue";
import AccountSearch from "../pages/outsourcing/AccountSearch.vue";
import IpAccountSearch from "../pages/outsourcing/IpAccountSearch.vue";
import UserDetail from "../pages/outsourcing/UserDetail.vue";
import KickPlayer from "../pages/outsourcing/KickPlayer.vue";
import ServerNotice from "../pages/outsourcing/ServerNotice.vue";
import Challenge from "../pages/Inside/Challenge.vue";
import HomeSav from "../pages/Inside/HomeSav.vue";
import GameSav from "../pages/Inside/GameSav.vue";
import HomeBanner from "../pages/Inside/HomeBanner.vue";
import Activity from "../pages/activity/Activity.vue";
import TaskCount from "../pages/activity/TaskCount.vue";
import LotteryCount from "../pages/activity/LotteryCount.vue";
import SpecialTask from "../pages/activity/SpecialTask.vue";
import SpecialLottery from "../pages/activity/SpecialLottery.vue";
import Inside from "../pages/Inside/Inside.vue";
import AbroadUnlock from "../pages/Inside/AbroadUnlock.vue";
import HackCheck from "../pages/Inside/HackCheck.vue";
import LiveManage from "../pages/Inside/LiveManage.vue";
import Service from "../pages/service/Service.vue";
import UnbindChinaid from "../pages/service/UnbindChinaid.vue";
import TradeSearch from "../pages/service/TradeSearch.vue";
import ClearTicket from "../pages/service/ClearTicket.vue";
import ClearNobleMonth from "../pages/service/ClearNobleMonth.vue";
import Statistics from "../pages/statistics/Statistics.vue";
import UserActive from "../pages/statistics/UserActive.vue";
import RechargeCount from "../pages/statistics/RechargeCount.vue";
import GameSales from "../pages/statistics/GameSales.vue";
import GameDetailSales from "../pages/statistics/GameDetailSales.vue";
import UsersBuyGameRecord from "../pages/statistics/UsersBuyGameRecord.vue";
import SettlementHistory from "../pages/statistics/SettlementHistory.vue";
import GameList from "../pages/statistics/GameList.vue";
import Tournament from "../pages/tournament/Tournament.vue";
import Tournaments from "../pages/tournament/TournamentList.vue";
import TournamentQuiz from "../pages/tournament/TournamentQuiz.vue";
import VersionManage from "../pages/gm/VersionManage.vue";
import { useMainStore } from "../store";

const routes = [
  {
    path: "/",
    redirect: "/home",
  },
  {
    path: "/login",
    name: "login",
    component: Login,
  },
  {
    path: "/home",
    name: "home",
    component: Home,
    redirect: "/games",
    children: [
      {
        path: "/welcome",
        name: "welcome",
        component: Welcome,
      },
      {
        path: "/user_detail",
        name: "userDetail",
        component: UserDetail,
      },
      {
        path: "/outsourcing",
        name: "outsourcing",
        component: Outsourcing,
        children: [
          {
            path: "nickname_review",
            name: "nicknameReview",
            component: NicknameReview,
          },
          {
            path: "avatar_review",
            name: "avatarReview",
            component: AvatarReview,
          },
          {
            path: "report_review",
            name: "reportReview",
            component: ReportReview,
          },
          {
            path: "account_search",
            name: "accountSearch",
            component: AccountSearch,
          },
          {
            path: "ip_account_search",
            name: "ipAccountSearch",
            component: IpAccountSearch,
          },
          {
            path: "kick_player",
            name: "kickPlayer",
            component: KickPlayer,
          },
          {
            path: "server_notice",
            name: "serverNotice",
            component: ServerNotice,
          },
        ],
      },
      {
        path: "/activity",
        name: "activity",
        component: Activity,
        children: [
          {
            path: "task_count",
            name: "taskCount",
            component: TaskCount,
          },
          {
            path: "lottery_count",
            name: "lotteryCount",
            component: LotteryCount,
          },
          {
            path: "special_task",
            name: "specialTask",
            component: SpecialTask,
          },
          {
            path: "special_lottery",
            name: "specialLottery",
            component: SpecialLottery,
          },
        ],
      },
      {
        path: "/inside",
        name: "inside",
        component: Inside,
        children: [
          {
            path: "abroad_unlock",
            name: "abroadUnlock",
            component: AbroadUnlock,
          },
          {
            path: "hack_check",
            name: "hackCheck",
            component: HackCheck,
          },
          {
            path: "challenge",
            name: "challenge",
            component: Challenge,
          },
          {
            path: "home_sav",
            name: "homeSav",
            component: HomeSav,
          },
          {
            path: "game_sav",
            name: "gameSav",
            component: GameSav,
          },
          {
            path: "home_banner",
            name: "homeBanner",
            component: HomeBanner,
          },
          {
            path: "live_manage",
            name: "liveManage",
            component: LiveManage,
          },
        ],
      },
      {
        path: "/service",
        name: "service",
        component: Service,
        children: [
          {
            path: "trade_search",
            name: "tradeSearch",
            component: TradeSearch,
          },
          {
            path: "unbind_chinaid",
            name: "unbindChinaid",
            component: UnbindChinaid,
          },
          {
            path: "clear_ticket",
            name: "clearTicket",
            component: ClearTicket,
          },
          {
            path: "clear_noble_month",
            name: "clearNobleMonth",
            component: ClearNobleMonth,
          },
        ],
      },
      {
        path: "/statistics",
        name: "statistics",
        component: Statistics,
        children: [
          {
            path: "user_active",
            name: "userActive",
            component: UserActive,
          },
          {
            path: "recharge_count",
            name: "rechargeCount",
            component: RechargeCount,
          },
          
          
        ],
      },
      {
        path: "/tournaments",
        name: "tournament",
        component: Tournament,
        children: [
          {
            path: "list",
            name: "tournaments",
            component: Tournaments,
          },
          {
            path: "quiz",
            name: "tournamentQuiz",
            component: TournamentQuiz,
          },
        ],
      },
      {
        path: "/games",
        name: "gameList",
        component: GameList,
      },
      {
        path: "/settlement",
        name: "settlement",
        component: SettlementHistory,
      },
      {
        path: "/users_record",
        name: "usersBuyGameRecord",
        component: UsersBuyGameRecord,
      },
      {
        path: "/game_sales",
        name: "gameSalversionManagees",
        component: GameSales,
      },
      
      {
        path: "/game_sales_detail",
        name: "gameSalesDetail",
        component: GameDetailSales,
      },
      {
        path: "/sales",
        name: "gameSalesData",
        component: GameDetailSales,
      },
      {
        path: "/version",
        name: "versionManage",
        component: VersionManage,
      },
    ],
  },
];

export const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from) => {
  if (to.name != "login") {
    const mainStore = useMainStore();
    mainStore.readUserInfo();
    // if (!mainStore.identityToken) {
    //   return { name: "login" };
    // }
  }
});
