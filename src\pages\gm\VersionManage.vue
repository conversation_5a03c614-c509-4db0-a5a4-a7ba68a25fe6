<script setup lang="ts">
import { ArrowRight, UploadFilled, Download, Refresh } from '@element-plus/icons-vue';
import { reactive, ref } from 'vue';
import { useMainStore } from '../../store';
import requset from '../../api';
import crypto from 'crypto-js';
import type { FormInstance, FormRules, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox } from 'element-plus';
const mainStore = useMainStore();

// 简单的CRC32计算函数
const calculateCRC32 = (data: Uint8Array): number => {
  const table = new Array(256);
  let crc = 0;

  // 生成CRC32表
  for (let i = 0; i < 256; i++) {
    crc = i;
    for (let j = 0; j < 8; j++) {
      crc = (crc & 1) ? (0xEDB88320 ^ (crc >>> 1)) : (crc >>> 1);
    }
    table[i] = crc;
  }

  // 计算CRC32
  crc = 0xFFFFFFFF;
  for (let i = 0; i < data.length; i++) {
    crc = table[(crc ^ data[i]) & 0xFF] ^ (crc >>> 8);
  }

  return (crc ^ 0xFFFFFFFF) >>> 0; // 转换为无符号32位整数
};

// Tab切换
const activeTab = ref('list');

// 版本列表相关
const loading = ref(false);
const selectedServer = ref('');
const versionList = ref([]);
const serverOptions = ref([
  { label: '全部', value: '' },
  { label: '测试服', value: 'test' },
  { label: '正式服', value: 'prod' }
]);

// 上传相关
const uploadRef = ref<UploadInstance>();
const uploadForm = reactive({
  file: null as File | null,
  fileName: '',
  versionCode: '',
  versionName: '',
  channelId: '',
  fileCrc: '',
  encryptedCrc: '',
  fileSize: '',
  upgradeInfo: '',
  upgradeServer: '全部',
  isTestUpgrade: false
});

const uploadFormRef = ref<FormInstance>();
const uploadRules = reactive<FormRules>({
  fileName: [
    { required: true, message: '请输入文件名', trigger: 'blur' }
  ],
  versionCode: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  versionName: [
    { required: true, message: '请输入版本名称', trigger: 'blur' }
  ],
  channelId: [
    { required: true, message: '请输入渠道号', trigger: 'blur' },
    { pattern: /^\d+$/, message: '渠道号必须为正整数', trigger: 'blur' }
  ],
  fileCrc: [
    { required: true, message: '请选择文件以自动计算CRC', trigger: 'blur' }
  ],
  encryptedCrc: [
    { required: true, message: '加密CRC将自动生成', trigger: 'blur' }
  ],
  fileSize: [
    { required: true, message: '请选择文件以自动计算文件大小', trigger: 'blur' }
  ]
});

const upgradeServerOptions = ref([
  { label: '全部', value: '全部' },
  { label: '测试服', value: '测试服' },
  { label: '正式服', value: '正式服' }
]);

// 计算文件大小的格式化显示
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 文件上传前的处理
const beforeUpload: UploadProps['beforeUpload'] = (rawFile: UploadRawFile) => {
  const isValidType = rawFile.type === 'application/vnd.android.package-archive' ||
                     rawFile.type === 'application/zip' ||
                     rawFile.name.toLowerCase().endsWith('.apk') ||
                     rawFile.name.toLowerCase().endsWith('.zip');

  if (!isValidType) {
    mainStore.globalMessageTip('只能上传APK或ZIP格式的文件', 3);
    return false;
  }

  // 处理文件
  handleFileSelect(rawFile);
  return false; // 阻止自动上传
};

// 处理文件选择
const handleFileSelect = async (file: File) => {
  uploadForm.file = file;

  // 如果是APK文件，修改后缀为zip
  let fileName = file.name;
  if (fileName.toLowerCase().endsWith('.apk')) {
    fileName = fileName.replace(/\.apk$/i, '.zip');
  }
  uploadForm.fileName = fileName;

  // 计算文件大小
  uploadForm.fileSize = file.size.toString();

  // 计算CRC
  try {
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    const crc = calculateCRC32(uint8Array);
    // 转换为十进制正数
    uploadForm.fileCrc = crc.toString();

    // 生成加密CRC (简单演示：使用MD5加密)
    uploadForm.encryptedCrc = crypto.MD5(uploadForm.fileCrc + 'secret_key').toString();
  } catch (error) {
    console.error('CRC计算失败:', error);
    mainStore.globalMessageTip('文件处理失败，请重新选择', 3);
    return;
  }

  // 分析文件名，提取渠道号和版本名称
  analyzeFileName(fileName);
};

// 分析文件名
const analyzeFileName = (fileName: string) => {
  // 匹配类似 EN12001_V1.2.1 的格式
  const match = fileName.match(/EN(\d+)_V([\d.]+)/i);
  if (match) {
    uploadForm.channelId = match[1]; // 渠道号
    uploadForm.versionName = 'V' + match[2].toUpperCase(); // 版本名称，强制大写
  }
};

// 获取版本列表
const getVersionList = async () => {
  loading.value = true;
  try {
    // 这里应该调用实际的API
    // const res = await requset.getVersionList({...});
    // 模拟数据
    versionList.value = [];
    mainStore.globalMessageTip('版本列表获取成功', 0);
  } catch (error) {
    console.error('获取版本列表失败:', error);
    mainStore.globalMessageTip('获取版本列表失败', 3);
  } finally {
    loading.value = false;
  }
};

// 确认上传
const confirmUpload = async () => {
  if (!uploadFormRef.value) return;

  await uploadFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await ElMessageBox.confirm(
          `确认上传版本文件？\n文件名：${uploadForm.fileName}\n版本：${uploadForm.versionName}\n渠道：${uploadForm.channelId}`,
          '确认上传',
          {
            confirmButtonText: '确认上传',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        await uploadVersion();
      } catch {
        // 用户取消
      }
    }
  });
};

// 上传版本
const uploadVersion = async () => {
  if (!uploadForm.file) {
    mainStore.globalMessageTip('请选择文件', 3);
    return;
  }

  mainStore.globalLoading(true);

  try {
    const formData = new FormData();
    formData.append('file', uploadForm.file);
    formData.append('fileName', uploadForm.fileName);
    formData.append('versionCode', uploadForm.versionCode);
    formData.append('versionName', uploadForm.versionName);
    formData.append('channelId', uploadForm.channelId);
    formData.append('fileCrc', uploadForm.fileCrc);
    formData.append('encryptedCrc', uploadForm.encryptedCrc);
    formData.append('fileSize', uploadForm.fileSize);
    formData.append('upgradeInfo', uploadForm.upgradeInfo);
    formData.append('upgradeServer', uploadForm.upgradeServer);
    formData.append('isTestUpgrade', uploadForm.isTestUpgrade.toString());

    // 添加认证信息
    formData.append('userID', mainStore.userInfo.user_id.toString());
    formData.append('identityToken', mainStore.identityToken);
    formData.append('token', crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString());

    // 这里应该调用实际的上传API
    // const res = await requset.uploadVersion(formData);

    // 模拟上传成功
    setTimeout(() => {
      mainStore.globalLoading(false);
      mainStore.globalMessageTip('版本上传成功', 0);
      resetUploadForm();
      // 刷新版本列表
      if (activeTab.value === 'list') {
        getVersionList();
      }
    }, 2000);

  } catch (error) {
    console.error('上传失败:', error);
    mainStore.globalLoading(false);
    mainStore.globalMessageTip('版本上传失败', 3);
  }
};

// 重置上传表单
const resetUploadForm = () => {
  uploadForm.file = null;
  uploadForm.fileName = '';
  uploadForm.versionCode = '';
  uploadForm.versionName = '';
  uploadForm.channelId = '';
  uploadForm.fileCrc = '';
  uploadForm.encryptedCrc = '';
  uploadForm.fileSize = '';
  uploadForm.upgradeInfo = '';
  uploadForm.upgradeServer = '全部';
  uploadForm.isTestUpgrade = false;

  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 初始化
getVersionList();
</script>


<template>
  <div class="version-manage">
    <el-space direction="vertical" style="width: 100%" fill>
      <el-breadcrumb :separator-icon="ArrowRight">
        <el-breadcrumb-item>GM管理</el-breadcrumb-item>
        <el-breadcrumb-item>版本管理</el-breadcrumb-item>
      </el-breadcrumb>

      <el-card class="content">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 版本列表 -->
          <el-tab-pane label="版本列表" name="list">
            <el-space direction="vertical" style="width: 100%" fill :size="20">
              <!-- 服务器选择器 -->
              <el-row>
                <el-col :span="6">
                  <el-form-item label="选择服务器：">
                    <el-select v-model="selectedServer" placeholder="请选择服务器" style="width: 200px">
                      <el-option
                        v-for="item in serverOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-button type="primary" :icon="Refresh" @click="getVersionList">刷新列表</el-button>
                </el-col>
              </el-row>

              <!-- 版本列表表格 -->
              <el-table v-loading="loading" :data="versionList" border style="width: 100%">
                <el-table-column prop="fileName" label="文件名" />
                <el-table-column prop="versionName" label="版本名称" />
                <el-table-column prop="versionCode" label="版本号" />
                <el-table-column prop="channelId" label="渠道号" />
                <el-table-column prop="fileSize" label="文件大小">
                  <template #default="scope">
                    {{ formatFileSize(parseInt(scope.row.fileSize)) }}
                  </template>
                </el-table-column>
                <el-table-column prop="upgradeServer" label="升级服务器" />
                <el-table-column prop="isTestUpgrade" label="测试升级">
                  <template #default="scope">
                    <el-tag :type="scope.row.isTestUpgrade ? 'warning' : 'success'">
                      {{ scope.row.isTestUpgrade ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="上传时间" />
                <el-table-column label="操作" width="120">
                  <template #default>
                    <el-button type="primary" size="small" :icon="Download">下载</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-space>
          </el-tab-pane>

          <!-- 上传版本 -->
          <el-tab-pane label="上传版本" name="upload">
            <el-space direction="vertical" style="width: 100%" fill :size="20">
              <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="120px">
                <!-- 文件上传区域 -->
                <el-form-item label="选择文件：" prop="file">
                  <el-upload
                    ref="uploadRef"
                    class="upload-demo"
                    drag
                    accept=".apk,.zip"
                    :before-upload="beforeUpload"
                    :limit="1"
                    :auto-upload="false"
                  >
                    <el-icon class="el-icon--upload">
                      <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                      将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                      <div class="el-upload__tip">
                        只能上传APK和ZIP格式的文件，APK文件将自动改为ZIP后缀
                      </div>
                    </template>
                  </el-upload>
                </el-form-item>

                <!-- 文件信息表单 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="文件名：" prop="fileName">
                      <el-input v-model="uploadForm.fileName" placeholder="请输入文件名" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="版本号：" prop="versionCode">
                      <el-input v-model="uploadForm.versionCode" placeholder="请输入版本号" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="版本名称：" prop="versionName">
                      <el-input v-model="uploadForm.versionName" placeholder="请输入版本名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="渠道号：" prop="channelId">
                      <el-input v-model="uploadForm.channelId" placeholder="请输入渠道号（正整数）" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="文件CRC：" prop="fileCrc">
                      <el-input v-model="uploadForm.fileCrc" placeholder="选择文件后自动计算" readonly />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="加密CRC：" prop="encryptedCrc">
                      <el-input v-model="uploadForm.encryptedCrc" placeholder="自动生成" readonly />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="文件大小：" prop="fileSize">
                      <el-input
                        :value="uploadForm.fileSize ? formatFileSize(parseInt(uploadForm.fileSize)) : ''"
                        placeholder="选择文件后自动计算"
                        readonly
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="升级服务器：">
                      <el-select v-model="uploadForm.upgradeServer" placeholder="请选择升级服务器">
                        <el-option
                          v-for="item in upgradeServerOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="升级信息：">
                  <el-input
                    v-model="uploadForm.upgradeInfo"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入升级信息"
                  />
                </el-form-item>

                <el-form-item label="测试升级：">
                  <el-switch v-model="uploadForm.isTestUpgrade" />
                  <span style="margin-left: 10px; color: #909399;">
                    {{ uploadForm.isTestUpgrade ? '是' : '否' }}
                  </span>
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" size="large" @click="confirmUpload">确认上传</el-button>
                  <el-button size="large" @click="resetUploadForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-space>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </el-space>
  </div>
</template>

<style lang="less" scoped>
.version-manage {
  .content {
    min-height: 600px;
  }

  .upload-demo {
    width: 100%;
  }

  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 7px;
  }
}
</style>