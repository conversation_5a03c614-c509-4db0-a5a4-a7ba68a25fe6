<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import { NicknameReview } from '../../interface';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);

getNicknameList();

async function getNicknameList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getNicknameList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.nicknameList) {
                    outsourcingStore.nicknameReviewList = res.data.nicknameList;
                    outsourcingStore.nicknameReviewNum = res.data.total;
                    outsourcingStore.nicknameReviewProcessed = res.data.processed;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        }).finally(() => {
            loading.value = false;
        });
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

async function correctNickname(targetID: number, nickname: string, flag: number) {
    if (targetID) {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${targetID}${flag}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            loading.value = true;
            await requset.correctUserNickname({
                userID: userID,
                targetUserID: targetID,
                identityToken: identityToken,
                authorityID: authorityID,
                handleType: flag,
                targetNickname: nickname,
                token: token
            }).then(res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('处理成功', 0);
                    outsourcingStore.removeTheNicknameReview(targetID);
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该用户已被其他管理员处理', 3);
                                outsourcingStore.removeTheNicknameReview(targetID);
                                break;
                            case 6:
                                mainStore.globalMessageTip('该用户昵称已更新', 3);
                                outsourcingStore.removeTheNicknameReview(targetID);
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                loading.value = false;
            });
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }
}

async function processAllNickname() {
    if (outsourcingStore.nicknameReviewList.length > 0) {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const targetUserID = outsourcingStore.nicknameReviewList.map(item => item.user_id).join(',');
        const token = crypto.SHA1(`${userID}${authorityID}${targetUserID}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            loading.value = true;
            await requset.processAllUserNickname({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: targetUserID,
                token: token
            }).then(res => {
                if (res.code === 0) {
                    getNicknameList();
                    mainStore.globalMessageTip('处理成功', 0);
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                loading.value = false;
            });
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    } else {
        mainStore.globalMessageTip('请刷新列表', 2);
    }
}

</script>

<template>
    <div class="nickname-review" v-if="mainStore.checkPermission(1102)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'outsourcing'
                }">
                    外协管理
                </el-breadcrumb-item>
                <el-breadcrumb-item>昵称审核</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>待处理任务条数：{{ outsourcingStore.nicknameReviewNum }}</span> <span>今日已处理：{{
                                    outsourcingStore.nicknameReviewProcessed
                                }}</span>
                                <el-button type="primary" @click="getNicknameList()" size="large">刷新列表
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table v-loading="loading" :data="outsourcingStore.nicknameReviewList" border style="width: 100%">
                        <el-table-column prop="nickname" label="昵称" />
                        <el-table-column label="ID">
                            <template #default="scope">
                                <el-link type="primary" :underline="false"
                                    @click="mainStore.gotoUserDetail(scope.row.user_id)">{{ scope.row.user_id }}</el-link>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-button type="danger" @click="correctNickname(scope.row.user_id, scope.row.nickname, 0)"
                                    size="large">昵称违规改名
                                </el-button>
                                <el-button type="danger" @click="correctNickname(scope.row.user_id, scope.row.nickname, 1)"
                                    size="large" v-if="scope.row.flag === 1">违规(补改名卡)
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-button type="success" size="large" @click="processAllNickname()">以上无问题
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                </el-space>


            </el-card>
        </el-space>


    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped></style>