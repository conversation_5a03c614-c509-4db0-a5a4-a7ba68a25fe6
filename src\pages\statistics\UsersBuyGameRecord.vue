<script setup lang="ts">
import { ArrowRight, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { computed, reactive, ref } from 'vue';
import { FormInstance, FormRules, genFileId, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox } from 'element-plus';
import Big from 'big.js';
import { useRoute } from 'vue-router';
import { router } from '../../router';
import { UserBuyGameRecordItem } from '../../interface';
import { getRegionLabel } from "../../data/region";

const route = useRoute();
const mainStore = useMainStore();

const loading = ref(false);
const currentPage = ref(1);
const dataTotal = ref(0);

const gid = ref(0);
if (route.query && route.query.gid) {
    gid.value = parseInt(route.query.gid.toString());
    if (!gid.value || gid.value <= 0) {
        router.push({
            name: 'home'
        });
        mainStore.globalMessageTip('GameId Error!', 3);
    }
}


const filterForm = reactive({
    company_id: '' as number | string,
    game_id: '' as number | string,
});

const usersBuyGameRecord = ref([] as Array<UserBuyGameRecordItem>);

await fetchCompanyMallGame();

getUsersBuyGameRecord();




function selectCompany(val: number) {
    mainStore.getGameList();
}

async function getUsersBuyGameRecord() {
    if (filterForm.game_id) {
        loading.value = true;
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${filterForm.game_id}${currentPage.value}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        await requset.getUsersBuyGameRecord({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            companyID: Number(filterForm.company_id),
            gameID: Number(filterForm.game_id),
            currentPage: currentPage.value,
            token: token,
        })
            .then((res) => {
                if (res.code === 0) {
                    if (res.data && res.data.usersBuyGameRecord) {
                        dataTotal.value = res.data.total;
                        usersBuyGameRecord.value = res.data.usersBuyGameRecord;
                        mainStore.lastStatisticsTime = res.data.update_time;
                    }
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            })
            .catch((err) => {
                console.log(err);
            })
            .finally(() => {
                loading.value = false;
            });;
    }
}

function currentPageChange(page: number) {
    currentPage.value = page;
    getUsersBuyGameRecord();
}

async function fetchCompanyMallGame() {
    mainStore.globalLoading(true);
    await mainStore.getGameCompanyList();
    if (mainStore.gameCompanyList[0]) {
        filterForm.company_id = mainStore.gameCompanyList[0].company_id;
    }
    if (filterForm.company_id) {
        await mainStore.getMallGameWithCompany(Number(filterForm.company_id));
    }
    if (mainStore.getterCurrentCompany(Number(filterForm.company_id)) && mainStore.getterCurrentCompany(Number(filterForm.company_id)).mall_games) {
        if (gid.value) {
            filterForm.game_id = gid.value;
        } else {
            filterForm.game_id = mainStore.getterCurrentCompany(Number(filterForm.company_id)).mall_games[0].game_id;
        }
    }
    mainStore.globalLoading(false);
}

function selectGame(val: number) {
    currentPage.value = 1;
    getUsersBuyGameRecord();
}

</script>

<template>
    <div class="banner-config">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item>
                    Manage
                </el-breadcrumb-item>
                <el-breadcrumb-item>User purchase history</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>Company: </span>
                                <el-select v-model="filterForm.company_id" placeholder="Please select company"
                                    size="large" @change="selectCompany">
                                    <el-option v-for="(item, index) in mainStore.gameCompanyList" :key="item.company_id"
                                        :label="item.company_name" :value="item.company_id" />
                                </el-select>

                                <el-button type="primary" size="large" @click="currentPageChange(1)">Refresh
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <span>Game: </span>
                                <el-select v-model="filterForm.game_id" placeholder="Please select a game" size="large"
                                    @change="selectGame">
                                    <el-option label="All" :value="-1" />
                                    <el-option
                                        v-for="(item, index) in mainStore.getterCurrentCompany(Number(filterForm.company_id))?.mall_games"
                                        :key="item.game_id" :label="item.game_name" :value="item.game_id" />
                                </el-select>
                                <b>Last update: {{ mainStore.lastStatisticsTime }} </b>
                                <span>(Hourly Statistics)</span>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table :data="usersBuyGameRecord" border style="width: 100%">
                        <!-- <el-table-column prop="game_id" label="ID" /> -->
                        <el-table-column prop="user_id" label="user" />
                        <el-table-column prop="chip_id" label="chip" />
                        <el-table-column label="region">
                            <template #default="scope">
                                {{ getRegionLabel(scope.row.region) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="channel" label="channel" />
                        <el-table-column label="Payment amount">
                            <template #default="scope">
                                ${{ new Big(scope.row.pay_price).div(100).toNumber() }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="buy_time" label="buy_time" />
                        <el-table-column label="Settlement">
                            <template #default="scope">
                                <el-text type="success" v-if="scope.row.settlement_flag === 1">YES</el-text>
                                <el-text type="info" v-else>NO</el-text>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="Game price">
                            <template #default="scope">
                                ${{ new Big(scope.row.price).div(100) }}
                            </template>
</el-table-column> -->
                    </el-table>
                    <el-pagination background layout="prev, pager, next" :total="dataTotal" :current-page="currentPage"
                        :page-size="15" @current-change="currentPageChange" />
                </el-space>
            </el-card>
        </el-space>
    </div>
</template>

<style lang="less" scoped></style>