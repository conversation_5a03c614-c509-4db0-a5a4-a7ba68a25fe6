<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { router } from '../../router';
import { AbroadUserState, UserChinaIdInfo, UserTicketInfo } from '../../interface';
import { ElMessageBox } from 'element-plus';
import Big from 'big.js';
import { utils, writeFile } from 'xlsx';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);
const tradeTableRef = ref();

const filterForm = reactive({
    nickname: '',
    account: '',
    user_id: '',
    find_type: 1
});

const userInfo = reactive({
    data: [] as Array<UserTicketInfo>
});

async function searchUsers() {
    if (!filterForm.account && !filterForm.nickname && !filterForm.user_id) {
        return mainStore.globalMessageTip('请输入查询信息!', 3);
    }
    if (filterForm.user_id !== '' && (parseInt(filterForm.user_id) < 0 || !parseInt(filterForm.user_id))) {
        return mainStore.globalMessageTip('请输入正确的用户ID!', 3);
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto.SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        loading.value = true;

        await requset.getUserTicketInfo({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            target_nickname: filterForm.nickname,
            target_account: filterForm.account,
            target_userID: parseInt(filterForm.user_id),
            find_type: filterForm.find_type,
            token: token
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.userInfo) {
                    if (res.data.userInfo.length > 0) {
                        let refund_total = new Big(new Big(res.data.userInfo[0].ticket).div(10).times(0.9).toFixed(1));
                        res.data.userInfo[0].refund_money = refund_total.toNumber();
                        res.data.userInfo[0].tradeList.forEach((item: any) => {
                            if (item.refund_flag === 0) {
                                let price = new Big(item.price);
                                if (refund_total.toNumber() > 0) {
                                    if (refund_total.minus(price.div(100)).toNumber() >= 0) {
                                        item.refund_money = price.div(100).toNumber();
                                        item.full_refund = 1;
                                    } else {
                                        item.refund_money = refund_total.toNumber();
                                        item.full_refund = 2;
                                    }
                                    refund_total = refund_total.minus(price.div(100));
                                } else {
                                    item.refund_money = 0;
                                    item.full_refund = 0;
                                }
                            } else {
                                item.refund_money = 0;
                                item.full_refund = 0;
                            }
                        });
                        if (refund_total.gt(0)) {
                            res.data.userInfo[0].trade_enough = 1;
                        } else {
                            res.data.userInfo[0].trade_enough = 0;
                        }
                        userInfo.data = res.data.userInfo;
                    }
                }
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('用户不存在', 3);
                            break;

                        default:
                            break;
                    }
                }
                userInfo.data = [];
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}



async function clearUsetTicket(uid: number) {
    if (!uid) {
        return;
    }
    ElMessageBox.confirm(`确定要清除该用户的点券吗?`, '清除点券', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${uid}${requset.ADMIN_TOKEN_KEY}`).toString();
        const tradeIds: number[] = [];
        userInfo.data[0].tradeList.forEach(item => {
            if (item.refund_flag === 0 && item.full_refund > 0) {
                tradeIds.push(item.id);
            }
        });
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.clearUserTicket({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetUserID: uid,
                refundTradeIds: tradeIds.join(','),
                token: token
            }).then(res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('清除成功', 0);
                    // searchUsers();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('用户剩余点券为0', 3);
                                break;
                            case 6:
                                mainStore.globalMessageTip('用户背包信息错误，请联系周玉华', 3);
                                break;
                            default:
                                break;
                        }
                    }

                }
            }).catch(err => {
                console.log(err);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}

async function updateTradeRefundFlag(id: number) {
    if (!id) {
        return;
    }
    ElMessageBox.confirm(`确定要标记为已退款吗?`, '标记退款', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto.SHA1(`${userID}${authorityID}${id}${requset.ADMIN_TOKEN_KEY}`).toString();
        if (userID && identityToken) {
            mainStore.globalLoading(true);
            await requset.updateTradeRefundFlag({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetID: id,
                token: token
            }).then(res => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('标记成功', 0);
                    userInfo.data[0].tradeList.some(item => {
                        if (item.id === id) {
                            item.refund_flag = 1;
                            return true;
                        }
                    });
                } else {
                    mainStore.dealResponseErrInfo(res.code)
                }
            }).catch(err => {
                console.log(err);
            });
            mainStore.globalLoading(false);
        } else {
            mainStore.dealResponseErrInfo(4);
        }
    }).catch(() => {

    });
}


function tradeTableRowClick(row: any, column: any, event: any) {
    tradeTableRef.value.toggleRowExpansion(row);
}




function exportExcel() {
    if (userInfo.data.length > 0) {
        const header = ["用户ID", "账号", "昵称", "剩余点券", "应退总额", "订单号", "支付渠道", "金额", "货币种类", "充值点券", "充值时间", "订单应退金额"];
        const rows: any[][] = [header];
        const date = new Date();
        const day = `${date.getFullYear()}-${(date.getMonth() + 1) < 10 ? '0' + '' + (date.getMonth() + 1) : date.getMonth() + 1}-${date.getDate() < 10 ? '0' + '' + date.getDate() : date.getDate()}`

        const fileName = `${day}_约战退款记录_${userInfo.data[0].user_id}_${userInfo.data[0].account}.xlsx`;

        userInfo.data[0].tradeList.forEach(item => {
            if (item.full_refund > 0) {
                const row = [
                    userInfo.data[0].user_id,
                    userInfo.data[0].account,
                    userInfo.data[0].nickname,
                    userInfo.data[0].ticket,
                    userInfo.data[0].refund_money,
                    item.trade_no,
                    item.pay_channel,
                    item.price / 100,
                    item.currency,
                    item.tickets,
                    item.time,
                    item.refund_money
                ];
                rows.push(row);
            }
        });

        const excel_name = '约战退款记录';

        const excel_file = utils.book_new();
        const excel_sheet = utils.aoa_to_sheet(rows);

        utils.book_append_sheet(excel_file, excel_sheet, excel_name);
        writeFile(excel_file, fileName);
    }
}

</script>

<template>
    <div class="account-search" v-if="mainStore.checkPermission(3003)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'service'
                }">
                    客服操作
                </el-breadcrumb-item>
                <el-breadcrumb-item>清除点券</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 50%" fill>
                    <el-form ref="ruleFormRef" :model="filterForm" status-icon class="login-form">
                        <el-form-item label="玩家昵称" label-width="120px">
                            <el-input placeholder="请输入玩家昵称" v-model="filterForm.nickname" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item label="玩家ID" label-width="120px">
                            <el-input placeholder="请输入玩家ID" v-model="filterForm.user_id" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item label="玩家账号" label-width="120px">
                            <el-input placeholder="请输入玩家账号" v-model="filterForm.account" size="large" maxlength="255" />
                        </el-form-item>
                        <el-form-item label-width="120px">
                            <el-button type="primary" size="large" @click="searchUsers()">确定查找</el-button>
                        </el-form-item>
                    </el-form>
                </el-space>
                <el-table :data="userInfo.data" border style="width: 100%" ref="tradeTableRef" :default-expand-all="true">
                    <el-table-column type="expand">
                        <template #default="props">
                            <el-row>
                                <el-col :span="1"></el-col>
                                <el-col :span="23">
                                    <el-table :data="props.row.tradeList" style="width: 100%" border>
                                        <el-table-column prop="id" label="ID" />
                                        <el-table-column prop="trade_no" label="订单号">
                                        </el-table-column>
                                        <el-table-column prop="pay_channel" label="支付渠道">
                                        </el-table-column>
                                        <el-table-column prop="price" label="金额">
                                            <template #default="scope">
                                                <span>{{ scope.row.price / 100 }}</span>
                                                <span>&nbsp;{{ scope.row.currency }}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="tickets" label="充值点券">
                                        </el-table-column>
                                        <el-table-column prop="time" label="支付时间">
                                        </el-table-column>
                                        <el-table-column label="是否退款">
                                            <template #default="scope">
                                                <span v-if="scope.row.refund_flag === 0" style="color: #67C23A;">正常</span>
                                                <span v-else-if="scope.row.refund_flag === 1"
                                                    style="color: #F56C6C;">已退款</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="应退金额">
                                            <template #default="scope">
                                                <el-text class="mx-1" :type="scope.row.full_refund === 2 ? 'danger' : ''"
                                                    size="large" v-if="scope.row.refund_flag === 0">{{
                                                        scope.row.refund_money }}</el-text>
                                                <!-- <span v-if="scope.row.refund_flag === 0">{{ scope.row.refund_money }}</span> -->
                                            </template>
                                        </el-table-column>
                                        <!-- <el-table-column label="操作">
                                                <template #default="scope">
                                                    <el-button type="danger"
                                                        @click.stop="updateTradeRefundFlag(scope.row.id)">标记退款
                                                    </el-button>
                                                </template>
                                            </el-table-column> -->
                                    </el-table>
                                </el-col>
                            </el-row>
                        </template>
                    </el-table-column>
                    <el-table-column prop="user_id" label="ID" />
                    <el-table-column prop="account" label="账号" />
                    <el-table-column prop="nickname" label="昵称">
                        <template #default="scope">
                            <el-link type="primary" :underline="false"
                                @click.stop="mainStore.gotoUserDetail(scope.row.user_id)">{{ scope.row.nickname }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="ticket" label="剩余点券">
                    </el-table-column>
                    <el-table-column label="应退金额">
                        <template #default="scope">
                            <el-text class="mx-1" :type="scope.row.trade_enough === 1 ? 'danger' : ''" size="large">{{
                                scope.row.refund_money }}</el-text>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作">
                        <template #default="scope">
                            <el-button type="primary" @click.stop="exportExcel()">导出表格
                            </el-button>
                            <el-button type="danger" @click.stop="clearUsetTicket(scope.row.user_id)">清除点券
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped></style>