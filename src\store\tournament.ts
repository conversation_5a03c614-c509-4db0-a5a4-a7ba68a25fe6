import { defineStore } from "pinia";
import { useMainStore } from "./index";
import { router } from "../router";
import { TournamentItem } from "../interface";

export const useTournamentStore = defineStore("tournament", {
  state: () => ({
    tournamentList: [] as Array<TournamentItem>,
    tournamentState: [
      {
        value: 0,
        label: "未开始",
        type: 'warning',
      },
      {
        value: 1,
        label: "正在进行",
        type: 'danger',
      },
      {
        value: 2,
        label: "已结束",
        type: 'info',
      },
    ],
    tournamentQuizState: [
      {
        value: 0,
        label: "未开始",
        type: 'warning',
      },
      {
        value: 1,
        label: "进行中",
        type: 'danger',
      },
      {
        value: 2,
        label: "已结束 待结算",
        type: 'primary',
      },
      {
        value: 3,
        label: "已开奖",
        type: 'info',
      },
    ],
  }),
  getters: {},
  actions: {
    async getTournamentList() {
        
    }
  },
});
