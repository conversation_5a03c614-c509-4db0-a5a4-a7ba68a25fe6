import {
  AbroadUserSearch,
  AddChallengeUserParams,
  AddGameSavParams,
  AddHomeBannerParams,
  AddHomeSavParams,
  AddLiveRoomParams,
  AddTournamentParams,
  AddTournamentQuizOptionParams,
  AddTournamentQuizParams,
  AddUserLiveParams,
  BanUserParams,
  CancelReportParams,
  ClearUserAvatarParams,
  ClearUserScoreParams,
  ClearUserTicketParams,
  CommonParams,
  CompanyIDParams,
  CorrcetAvatarParams,
  CorrcetNicknameParams,
  CorrcetUserParams,
  DeleteHomeSavParams,
  DeleteTournamentQuizOptionParams,
  EditGameSavParams,
  EditHomeBannerParams,
  EditHomeSavParams,
  EditTournamentParams,
  EditTournamentQuizOptionParams,
  EditTournamentQuizParams,
  GetGameSavParams,
  GetHackCheckListParams,
  GetLoginSecretParams,
  GetReportParams,
  GetTournamentQuizParams,
  GetUserBuyGameRecordParams,
  GetUsersActivityTaskParams,
  GetUsersLotteryParams,
  GetUsersTaskParams,
  IpParam,
  KickPlayerParams,
  LoginParams,
  ProcessAllAvatarParams,
  ProcessAllUserParams,
  QueryBatchIpParams,
  RefundRechargeTicketParams,
  ReportHandleParams,
  SendNoticeParams,
  SendVerifySmsCodeParams,
  SetUserVerifyModeParams,
  SortHomeSavParams,
  StatisticsRechargeDataParams,
  StatisticsUserActiveDataParams,
  TradeNoSearchParams,
  UnfreezeUserParams,
  UserSearchParams,
} from "../interface";
import { service, API_ROOT } from "../utils/axios";

export default {
  ADMIN_TOKEN_KEY: "2qfVV4X6F0IuXFq6",
  ADMIN_SMS_KEY: "bB0bG0cG3gC1aG6a",
  ADMIN_SECRET_KEY1: 0x33c70e78,
  ADMIN_SECRET_KEY2: 0x4759a3b1,
  uploadFileApi: API_ROOT + "upload_img.php",
  getLoginSmsSecretCode(data: GetLoginSecretParams) {
    return service({
      url: "get_login_sms_secret_code.php",
      method: "POST",
      data: data,
    });
  },
  sendVerifySmsCode(data: SendVerifySmsCodeParams) {
    return service({
      url: "send_verify_sms_code.php",
      method: "POST",
      data: data,
    });
  },
  login(data: LoginParams) {
    return service({
      url: "login.php",
      method: "POST",
      data: data,
    });
  },
  getNicknameList(params: CommonParams) {
    return service({
      url: "get_nickname_list.php",
      method: "GET",
      params: params,
    });
  },
  correctUserNickname(data: CorrcetNicknameParams) {
    return service({
      url: "correct_user_nickname_v2.php",
      method: "POST",
      data: data,
    });
  },
  processAllUserNickname(data: ProcessAllUserParams) {
    return service({
      url: "process_all_nickname.php",
      method: "POST",
      data: data,
    });
  },
  getAvatarList(params: CommonParams) {
    return service({
      url: "get_avatar_list.php",
      method: "GET",
      params: params,
    });
  },
  correctUserAvatar(data: CorrcetAvatarParams) {
    return service({
      url: "correct_user_avatar_v2.php",
      method: "POST",
      data: data,
    });
  },
  processAllUserAvatar(data: ProcessAllAvatarParams) {
    return service({
      url: "process_all_avatar.php",
      method: "POST",
      data: data,
    });
  },
  getGameList(params: CommonParams) {
    return service({
      url: "get_game_list.php",
      method: "GET",
      params: params,
    });
  },
  getMallGameWithCompany(params: CompanyIDParams) {
    return service({
      url: "get_mall_game_with_company.php",
      method: "GET",
      params: params,
    });
  },
  getGameCompanySettlement(params: CompanyIDParams) {
    return service({
      url: "get_game_company_settlement.php",
      method: "GET",
      params: params,
    });
  },
  getAllReviewNum(params: CommonParams) {
    return service({
      url: "get_all_review_num.php",
      method: "GET",
      params: params,
    });
  },
  getGameCompanyList(params: CommonParams) {
    return service({
      url: "get_game_company_list.php",
      method: "GET",
      params: params,
    });
  },
  getReportList(params: GetReportParams) {
    return service({
      url: "get_report_list.php",
      method: "GET",
      params: params,
    });
  },
  cancelReportTask(data: CancelReportParams) {
    return service({
      url: "cancel_report_task.php",
      method: "POST",
      data: data,
    });
  },
  getUsersTaskRecord(params: GetUsersTaskParams) {
    return service({
      url: "get_users_task_record.php",
      method: "GET",
      params: params,
    });
  },
  getUsersLotteryRecord(params: GetUsersLotteryParams) {
    return service({
      url: "get_users_lottery_record.php",
      method: "GET",
      params: params,
    });
  },
  getTargetUserAvatarList(params: CorrcetUserParams) {
    return service({
      url: "get_user_avatar_list.php",
      method: "GET",
      params: params,
    });
  },
  reportHandle(data: ReportHandleParams) {
    return service({
      url: "report_handle.php",
      method: "POST",
      data: data,
    });
  },
  userSearch(params: UserSearchParams) {
    return service({
      url: "user_search.php",
      method: "GET",
      params: params,
    });
  },
  banUser(data: BanUserParams) {
    return service({
      url: "ban_user.php",
      method: "POST",
      data: data,
    });
  },
  unfreezeUser(data: UnfreezeUserParams) {
    return service({
      url: "unfreeze_user.php",
      method: "POST",
      data: data,
    });
  },
  getUsersActivityTaskRecord(params: GetUsersActivityTaskParams) {
    return service({
      url: "get_users_activity_task_record.php",
      method: "GET",
      params: params,
    });
  },
  getUsersActivityLotteryRecord(params: GetUsersActivityTaskParams) {
    return service({
      url: "get_users_activity_lottery_record.php",
      method: "GET",
      params: params,
    });
  },
  getUserBaseInfo(params: CorrcetUserParams) {
    return service({
      url: "get_user_base_info.php",
      method: "GET",
      params: params,
    });
  },
  getUserDetailInfo(params: CorrcetUserParams) {
    return service({
      url: "get_user_detail_info.php",
      method: "GET",
      params: params,
    });
  },
  clearUserGameScore(data: ClearUserScoreParams) {
    return service({
      url: "clear_user_game_score.php",
      method: "POST",
      data: data,
    });
  },
  addUserLiveRoom(data: AddLiveRoomParams) {
    return service({
      url: "add_user_live_room.php",
      method: "POST",
      data: data,
    });
  },
  deleteUserLiveRoom(data: CorrcetUserParams) {
    return service({
      url: "delete_user_live_room.php",
      method: "POST",
      data: data,
    });
  },
  clearUserAvatar(data: ClearUserAvatarParams) {
    return service({
      url: "delete_user_avatar.php",
      method: "POST",
      data: data,
    });
  },
  clearUserNickname(data: CorrcetNicknameParams) {
    return service({
      url: "clear_user_nickname.php",
      method: "POST",
      data: data,
    });
  },
  clearUserPhone(data: CorrcetUserParams) {
    return service({
      url: "unbind_user_phone.php",
      method: "POST",
      data: data,
    });
  },
  setUserVerifyMode(data: SetUserVerifyModeParams) {
    return service({
      url: "set_user_sms_verify.php",
      method: "POST",
      data: data,
    });
  },
  getServerNoticeList(params: GetUsersTaskParams) {
    return service({
      url: "get_server_notice.php",
      method: "GET",
      params: params,
    });
  },
  sendServerNotice(data: SendNoticeParams) {
    return service({
      url: "send_server_notice.php",
      method: "POST",
      data: data,
    });
  },
  getMenuList(params: CommonParams) {
    return service({
      url: "get_menu_list.php",
      method: "GET",
      params: params,
    });
  },
  getAbroadUserState(params: AbroadUserSearch) {
    return service({
      url: "query_abroad_user_state.php",
      method: "GET",
      params: params,
    });
  },
  abroadUserUnlock(data: CorrcetUserParams) {
    return service({
      url: "abroad_user_unlock.php",
      method: "POST",
      data: data,
    });
  },
  kickPlayer(data: KickPlayerParams) {
    return service({
      url: "kick_player.php",
      method: "POST",
      data: data,
    });
  },
  getHackCheckList(params: GetHackCheckListParams) {
    return service({
      url: "get_hack_check_list.php",
      method: "GET",
      params: params,
    });
  },
  getUserChinaIdInfo(params: AbroadUserSearch) {
    return service({
      url: "get_user_chinaid_info.php",
      method: "GET",
      params: params,
    });
  },
  getUserTicketInfo(params: AbroadUserSearch) {
    return service({
      url: "get_user_ticket_info.php",
      method: "GET",
      params: params,
    });
  },
  unbindUserChinaId(data: CorrcetUserParams) {
    return service({
      url: "unbind_user_china_id.php",
      method: "POST",
      data: data,
    });
  },
  tradeNoSearch(params: TradeNoSearchParams) {
    return service({
      url: "trade_no_search.php",
      method: "GET",
      params: params,
    });
  },
  statisticsUserActiveData(params: StatisticsUserActiveDataParams) {
    return service({
      url: "statistics_user_new_active_data.php",
      method: "GET",
      params: params,
    });
  },
  statisticsGameSalesData(params: any) {
    return service({
      url: "statistics_game_sales_data.php",
      method: "GET",
      params: params,
    });
  },
  statisticsGameSalesDetailData(params: any) {
    return service({
      url: "statistics_game_sales_deatail_data.php",
      method: "GET",
      params: params,
    });
  },
  settlementAllCompanyGame(data: CompanyIDParams) {
    return service({
      url: "settlement_company_all_game.php",
      method: "POST",
      data: data,
    });
  },
  statisticsGameSalesTableData(params: any) {
    return service({
      url: "statistics_game_sales_table_data.php",
      method: "GET",
      params: params,
    });
  },
  getAppChannel(params: CommonParams) {
    return service({
      url: "get_app_channel.php",
      method: "GET",
      params: params,
    });
  },
  getServerList(params: CommonParams) {
    return service({
      url: "get_all_server.php",
      method: "GET",
      params: params,
    });
  },
  refundRechargeTicket(data: RefundRechargeTicketParams) {
    return service({
      url: "refund_recharge_ticket.php",
      method: "POST",
      data: data,
    });
  },
  clearUserTicket(data: ClearUserTicketParams) {
    return service({
      url: "clear_user_ticket_v2.php",
      method: "POST",
      data: data,
    });
  },
  updateTradeRefundFlag(data: DeleteHomeSavParams) {
    return service({
      url: "update_trade_refund_flag.php",
      method: "POST",
      data: data,
    });
  },
  statisticsRechargeData(params: StatisticsRechargeDataParams) {
    return service({
      url: "statistics_recharge_data.php",
      method: "GET",
      params: params,
    });
  },
  addChallengeRoomUser(data: AddChallengeUserParams) {
    return service({
      url: "add_challenge_user.php",
      method: "POST",
      data: data,
    });
  },
  getChallengeUserList(params: GetUsersLotteryParams) {
    return service({
      url: "get_challenge_user_list.php",
      method: "GET",
      params: params,
    });
  },
  cancelUserChallenge(data: CorrcetUserParams) {
    return service({
      url: "cancel_user_challenge.php",
      method: "POST",
      data: data,
    });
  },
  getHomePageRecSav(params: CommonParams) {
    return service({
      url: "get_homepage_rec_sav.php",
      method: "GET",
      params: params,
    });
  },
  addHomePageRecSav(data: AddHomeSavParams) {
    return service({
      url: "add_homepage_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  editHomePageRecSav(data: EditHomeSavParams) {
    return service({
      url: "edit_homepage_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  deleteHomePageRecSav(data: DeleteHomeSavParams) {
    return service({
      url: "delete_homepage_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  sortHomePageRecSav(data: SortHomeSavParams) {
    return service({
      url: "sort_homepage_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  getGamePageRecSav(params: GetGameSavParams) {
    return service({
      url: "get_game_rec_sav.php",
      method: "GET",
      params: params,
    });
  },
  addGamePageRecSav(data: AddGameSavParams) {
    return service({
      url: "add_game_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  editGamePageRecSav(data: EditGameSavParams) {
    return service({
      url: "edit_game_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  deleteGamePageRecSav(data: DeleteHomeSavParams) {
    return service({
      url: "delete_gamepage_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  sortGamePageRecSav(data: SortHomeSavParams) {
    return service({
      url: "sort_gamepage_rec_sav.php",
      method: "POST",
      data: data,
    });
  },
  queryBatchIpInfo(params: QueryBatchIpParams) {
    return service({
      url: "query_batch_ip_info.php",
      method: "GET",
      params: params,
    });
  },
  getHomeBannerList(params: CommonParams) {
    return service({
      url: "get_homepage_banner.php",
      method: "GET",
      params: params,
    });
  },
  addHomeBanner(data: AddHomeBannerParams) {
    return service({
      url: "add_homepage_banner.php",
      method: "POST",
      data: data,
    });
  },
  editHomeBanner(data: EditHomeBannerParams) {
    return service({
      url: "edit_homepage_banner.php",
      method: "POST",
      data: data,
    });
  },
  deleteHomeBanner(data: DeleteHomeSavParams) {
    return service({
      url: "delete_homepage_banner.php",
      method: "POST",
      data: data,
    });
  },
  getUserLiveList(params: GetUsersLotteryParams) {
    return service({
      url: "get_user_live_list.php",
      method: "GET",
      params: params,
    });
  },
  addUserLive(data: AddUserLiveParams) {
    return service({
      url: "add_user_live.php",
      method: "POST",
      data: data,
    });
  },
  editUserLive(data: AddUserLiveParams) {
    return service({
      url: "edit_user_live.php",
      method: "POST",
      data: data,
    });
  },
  getGameQuizList(data: AddUserLiveParams) {
    return service({
      url: "edit_user_live.php",
      method: "POST",
      data: data,
    });
  },
  addUserReportBlack(data: CorrcetUserParams) {
    return service({
      url: "add_user_report_black.php",
      method: "POST",
      data: data,
    });
  },
  removeUserReportBlack(data: CorrcetUserParams) {
    return service({
      url: "remove_user_report_black.php",
      method: "POST",
      data: data,
    });
  },
  getAccountListWithIp(params: IpParam) {
    return service({
      url: "get_player_all_account_with_ip.php",
      method: "GET",
      params: params,
    });
  },
  getTournamentList(params: CommonParams) {
    return service({
      url: "get_tournament_list.php",
      method: "GET",
      params: params,
    });
  },
  addTournament(data: AddTournamentParams) {
    return service({
      url: "add_tournament.php",
      method: "POST",
      data: data,
    });
  },
  editTournament(data: EditTournamentParams) {
    return service({
      url: "edit_tournament.php",
      method: "POST",
      data: data,
    });
  },
  deleteTournament(data: DeleteHomeSavParams) {
    return service({
      url: "delete_tournament.php",
      method: "POST",
      data: data,
    });
  },
  getTournamentQuizList(params: GetTournamentQuizParams) {
    return service({
      url: "get_tournament_quiz_list.php",
      method: "GET",
      params: params,
    });
  },
  addTournamentQuiz(data: AddTournamentQuizParams) {
    return service({
      url: "add_tournament_quiz.php",
      method: "POST",
      data: data,
    });
  },
  editTournamentQuiz(data: EditTournamentQuizParams) {
    return service({
      url: "edit_tournament_quiz.php",
      method: "POST",
      data: data,
    });
  },
  deleteTournamentQuiz(data: DeleteHomeSavParams) {
    return service({
      url: "delete_tournament_quiz.php",
      method: "POST",
      data: data,
    });
  },
  addTournamentQuizOption(data: AddTournamentQuizOptionParams) {
    return service({
      url: "add_tournament_quiz_option.php",
      method: "POST",
      data: data,
    });
  },
  editTournamentQuizOption(data: EditTournamentQuizOptionParams) {
    return service({
      url: "edit_tournament_quiz_option.php",
      method: "POST",
      data: data,
    });
  },
  deleteTournamentQuizOption(data: DeleteTournamentQuizOptionParams) {
    return service({
      url: "delete_tournament_quiz_option.php",
      method: "POST",
      data: data,
    });
  },
  tournamentQuizSettlement(data: DeleteTournamentQuizOptionParams) {
    return service({
      url: "tournament_quiz_settlement.php",
      method: "POST",
      data: data,
    });
  },
  getUserNobleMemberInfo(params: AbroadUserSearch) {
    return service({
      url: "get_user_member_noble_info.php",
      method: "GET",
      params: params,
    });
  },
  clearUserNobleMember(data: ClearUserTicketParams) {
    return service({
      url: "clear_user_noble_member.php",
      method: "POST",
      data: data,
    });
  },
  getUsersBuyGameRecord(params: GetUserBuyGameRecordParams) {
    return service({
      url: "get_user_buy_game_record.php",
      method: "GET",
      params: params,
    });
  },
};
