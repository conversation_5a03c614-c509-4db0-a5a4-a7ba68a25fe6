import { defineStore } from "pinia";
import { ElMessage } from "element-plus";
import { router } from "../router";
import { ElLoading } from "element-plus";
import {
  AdminMenu,
  AppChannel,
  FormSelectType,
  GameCompanyItem,
  GameItem,
  GameSeries,
  GameServer,
  MallGameItem,
  ServerRegion,
} from "../interface";
import { useOutsourceingStore } from "./outsourcing";
import crypto from "crypto-js";
import requset from "../api";

let fullLoading: any;

export const useMainStore = defineStore("main", {
  state: () => ({
    userInfo: {
      user_id: 0,
      name: "",
      role: "",
    },
    lastStatisticsTime: "",
    identityToken: "",
    serverList: [] as Array<ServerRegion>,
    gameList: [] as Array<GameItem>,
    shopGameList: [] as Array<GameItem>,
    gameSeries: [] as Array<GameSeries>,
    gameServerList: [] as Array<GameServer>,
    menuList: [] as Array<AdminMenu>,
    gameCompanyList: [] as Array<GameCompanyItem>,
    channelList: [] as Array<AppChannel>,
    permissionList: [] as Array<number>,
    channelCountType: [] as Array<FormSelectType>,
    bannerGotoType: [
      {
        value: 0,
        label: "不需要跳转",
      },
      {
        value: 1,
        label: "外部网页",
      },
      {
        value: 2,
        label: "本地网页弹窗",
      },
      {
        value: 3,
        label: "本地页面",
      },
      {
        value: 4,
        label: "播放录像",
      },
      {
        value: 5,
        label: "语音直播",
      },
      {
        value: 6,
        label: "盛天页游",
      },
    ],
    recSavCurrency: [
      {
        id: 1,
        name: "金币",
      },
    ],
    vtPhoneStrList: [
      "10641",
      "162",
      "165",
      "167",
      "1700",
      "1701",
      "1702",
      "1703",
      "1705",
      "1706",
      "1704",
      "1707",
      "1708",
      "1709",
      "171",
    ],
  }),
  getters: {
    getterFirstMenu(state) {
      return state.menuList.filter(
        (item) => item.extends === 0 && item.path_name
      );
    },
    getterCurrentCompany(state) {
      return (company_id: number) => {
        const index = state.gameCompanyList.findIndex(
          (item) => item.company_id === company_id
        );
        if (index >= 0) {
          return (
            state.gameCompanyList[index]
          );
        } else {
          return {} as GameCompanyItem
        }
      };
    },
  },
  actions: {
    readUserInfo() {
      if (!this.identityToken || !this.userInfo.user_id) {
        const userData = localStorage.getItem("userInfo");
        if (userData) {
          const userDataJson = JSON.parse(userData);
          if (userDataJson) {
            this.$patch({
              userInfo: {
                user_id: userDataJson.user_id,
                name: userDataJson.name,
                role: userDataJson.role,
              },
              identityToken: userDataJson.login_token,
            });
          }
        } else {
          this.$patch({
            userInfo: {
              user_id: 0,
              name: "",
              role: "",
            },
            identityToken: "",
          });
          router.push("/login");
        }
      }
    },
    logout() {
      this.$patch({
        userInfo: {
          user_id: 0,
          name: "",
          role: "",
        },
        identityToken: "",
      });
      localStorage.removeItem("userInfo");
      router.push("/login");
    },
    checkPermission(perm_id: number) {
      return this.permissionList.some((item) => {
        if (item === perm_id || item === -1) {
          return true;
        }
      });
    },
    async getGameList() {
      const userID = this.userInfo.user_id;
      const identityToken = this.identityToken;
      const authorityID = 10102;
      const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
      const outsourcingStore = useOutsourceingStore();
      if (userID && identityToken) {
        requset
          .getGameList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
          })
          .then((res) => {
            if (res.code === 0) {
              if (res.data) {
                this.gameSeries = res.data.gameSeries;
                this.gameList = res.data.gameList;
                this.shopGameList = res.data.shopGameList;
                this.shopGameList.unshift({
                  game_id: -1,
                  game_name: "ALL",
                  series_id: 0,
                });
                this.gameServerList = res.data.serverList;
                outsourcingStore.reportGameSeriesOptions = this.gameSeries.map(
                  (item) => ({
                    value: item.series_id,
                    label: item.series_name,
                  })
                );
                outsourcingStore.reportGameSeriesOptions.unshift({
                  value: -1,
                  label: "全部游戏",
                });
                outsourcingStore.reportGameListOptions = this.gameList.map(
                  (item) => ({
                    value: item.game_id,
                    label: item.game_name,
                  })
                );
              }
            } else {
              this.dealResponseErrInfo(res.code);
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {});
      } else {
        this.dealResponseErrInfo(4);
      }
    },
    async getGameCompanyList() {
      const userID = this.userInfo.user_id;
      const identityToken = this.identityToken;
      const authorityID = 10102;
      const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
      if (userID && identityToken) {
        await requset
          .getGameCompanyList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
          })
          .then((res) => {
            if (res.code === 0) {
              if (res.data) {
                res.data.companyList.forEach((item1: GameCompanyItem) => {
                  const index = this.gameCompanyList.findIndex(
                    (item2) => item2.company_id === item1.company_id
                  );
                  if (index === -1) {
                    this.gameCompanyList.push(item1);
                  }
                });
              }
            } else {
              this.dealResponseErrInfo(res.code);
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {});
      } else {
        this.dealResponseErrInfo(4);
      }
    },
    async getMallGameWithCompany(company_id: number) {
      const userID = this.userInfo.user_id;
      const identityToken = this.identityToken;
      const authorityID = 10102;
      const token = crypto
        .SHA1(`${userID}${authorityID}${company_id}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
      if (userID && identityToken) {
        await requset
          .getMallGameWithCompany({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            companyID: company_id,
            token: token,
          })
          .then((res) => {
            if (res.code === 0) {
              if (res.data && res.data.mallGameList) {
                this.gameCompanyList.some((item) => {
                  if (item.company_id === company_id) {
                    item.mall_games = res.data.mallGameList;
                    item.all_unsettled_revenue = res.data.allUnsettledRevenue;
                    return true;
                  }
                });
                this.lastStatisticsTime = res.data.update_time;
              }
            } else {
              this.dealResponseErrInfo(res.code);
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {});
      } else {
        this.dealResponseErrInfo(4);
      }
    },
    async getMenuList() {
      const userID = this.userInfo.user_id;
      const identityToken = this.identityToken;
      const authorityID = 10102;
      const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
      const outsourcingStore = useOutsourceingStore();
      if (userID && identityToken) {
        this.globalLoading(true);
        await requset
          .getMenuList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
          })
          .then((res) => {
            if (res.code === 0) {
              if (res.data) {
                if (res.data.name) {
                  this.userInfo.name = res.data.name;
                }
                if (res.data.role) {
                  this.userInfo.role = res.data.role;
                }
                if (res.data.premissionList) {
                  this.permissionList = res.data.premissionList;
                }
                if (res.data.menuList && res.data.menuList.length > 0) {
                  this.menuList = this.generateMenuOptions(res.data.menuList);
                }
              }
            } else {
              this.dealResponseErrInfo(res.code);
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {});
        this.globalLoading(false);
      } else {
        this.dealResponseErrInfo(4);
      }
    },
    generateMenuOptions(arr: Array<any>) {
      //生成Cascader级联数据
      const result: any[] = [];
      arr.forEach((item1) => {
        if (item1.extends === 0) {
          item1.children = this.getMenuChilds(item1.menu_id, arr);
          result.push(item1);
        }
      });
      return result;
    },
    async fetchAppChannelList() {
      const userID = this.userInfo.user_id;
      const identityToken = this.identityToken;
      const authorityID = 10102;
      const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
      if (userID && identityToken) {
        await requset
          .getAppChannel({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
          })
          .then((res) => {
            if (res.code === 0) {
              if (res.data && res.data.channelList) {
                this.channelList = res.data.channelList;
                const flag = this.channelList.some((item) => {
                  if (item.channel_id === -1) {
                    return true;
                  }
                });
                if (flag) {
                  this.channelCountType = [
                    {
                      label: "标准统计",
                      value: 1,
                    },
                    {
                      label: "注册统计",
                      value: 2,
                    },
                    {
                      label: "渠道统计",
                      value: 3,
                    },
                  ];
                } else {
                  this.channelCountType = [
                    {
                      label: "注册统计",
                      value: 2,
                    },
                  ];
                }
              }
            } else {
              this.dealResponseErrInfo(res.code);
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {});
      } else {
        this.dealResponseErrInfo(4);
      }
    },
    async fetchServerList() {
      const userID = this.userInfo.user_id;
      const identityToken = this.identityToken;
      const authorityID = 10102;
      const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
      if (userID && identityToken) {
        await requset
          .getServerList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
          })
          .then((res) => {
            if (res.code === 0) {
              if (res.data && res.data.serverList) {
                this.serverList = res.data.serverList;
              }
            } else {
              this.dealResponseErrInfo(res.code);
            }
          })
          .catch((err) => {
            console.log(err);
          })
          .finally(() => {});
      } else {
        this.dealResponseErrInfo(4);
      }
    },
    getMenuChilds(id: any, array: any) {
      let childs = new Array();
      array.forEach((item: { extends: any }) => {
        //循环获取子节点
        if (item.extends == id) {
          childs.push(item);
        }
      });
      childs.forEach((item) => {
        //获取子节点的子节点
        let childscopy = this.getMenuChilds(item.menu_id, array); //递归获取子节点
        if (childscopy.length > 0) {
          item.children = childscopy;
        }
      });
      return childs;
    },
    queryMenuPermission(arr: Array<AdminMenu>, mid: number) {
      const flag = arr.some((item1) => {
        if (item1.menu_id === mid) {
          return true;
        }
        if (item1.children && item1.children.length > 0) {
          if (this.queryMenuPermission(item1.children, mid) === true) {
            return true;
          }
        }
      });
      return flag;
    },
    dealResponseErrInfo(code: number) {
      switch (code) {
        case -1:
          this.globalMessageTip("网络异常，请检查网络连接是否正常", 3);
          return true;
        case 1:
        case 2:
          this.globalMessageTip("提交参数有误", 3);
          return true;
        case 3:
          this.globalMessageTip("Server Error!", 3);
          return true;
        case 4:
          this.globalMessageTip("登录令牌过期，请重新登录", 3);
          this.$patch({
            userInfo: {
              user_id: 0,
              name: "",
            },
            identityToken: "",
          });
          localStorage.removeItem("userInfo");
          router.push({
            name: "login",
          });
          return true;
        case 402:
          this.globalMessageTip("没有该渠道的权限!", 3);
          return true;
        case 403:
          this.globalMessageTip("权限不足, 请联系管理员!", 3);
          return true;
        default:
          return false;
      }
    },
    globalMessageTip(msg: string, type: number) {
      switch (type) {
        case 0:
          ElMessage({
            type: "success",
            message: msg,
          });
          break;
        case 1:
          ElMessage({
            type: "warning",
            message: msg,
          });
          break;
        case 2:
          ElMessage({
            type: "info",
            message: msg,
          });
          break;
        case 3:
          ElMessage({
            type: "error",
            message: msg,
          });
          break;
        default:
          ElMessage({
            type: "info",
            message: msg,
          });
          break;
      }
    },
    globalLoading(display: boolean) {
      if (display) {
        fullLoading = ElLoading.service({
          lock: true,
          text: "Loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
      } else {
        fullLoading.close();
      }
    },
    getGameName(game_id: number) {
      if (game_id === -1) {
        return "首页";
      } else {
        const index = this.gameList.findIndex(
          (item) => item.game_id === game_id
        );
        if (index >= 0) {
          return this.gameList[index].game_name;
        } else {
          return game_id;
        }
      }
    },
    getGameServerName(game_id: number, server_id: number) {
      const index = this.gameServerList.findIndex(
        (item) => item.game_id === game_id
      );
      if (index >= 0) {
        const index2 = this.gameServerList[index].server_list.findIndex(
          (item) => item.server_id === server_id + 1
        );
        if (index2 >= 0) {
          return this.gameServerList[index].server_list[index2].server_name;
        }
      }
      return server_id;
    },
    getRoomName(room_id: number) {
      if (room_id === -1) {
        return "大厅";
      } else if (room_id >= 0 && room_id < 200) {
        return room_id + 1 + "号房";
      } else if (room_id >= 200) {
        return room_id - 200 + 1 + "号擂台房";
      } else {
        return room_id;
      }
    },
    getBannerGotoTypeName(type: number) {
      const index = this.bannerGotoType.findIndex(
        (item) => item.value === type
      );
      if (index >= 0) {
        return this.bannerGotoType[index].label;
      }
      return type;
    },
    gotoUserDetail(uid: number) {
      if (uid && uid > 0) {
        window.open(
          window.location.origin +
            window.location.pathname +
            "#/user_detail?uid=" +
            uid,
          "_blank"
        );
      } else {
        this.globalMessageTip("数据错误，请刷新页面", 3);
      }
    },
    gotoGameSalesDetail(gid: number) {
      if (gid && gid > 0) {
        window.open(
          window.location.origin +
            window.location.pathname +
            "#/game_sales_detail?gid=" +
            gid,
          "_blank"
        );
      } else {
        this.globalMessageTip("数据错误，请刷新页面", 3);
      }
    },
    gotoUsersBuyGameRecord(gid: number) {
      if (gid && gid > 0) {
        window.open(
          window.location.origin +
            window.location.pathname +
            "#/users_record?gid=" +
            gid,
          "_blank"
        );
      } else {
        this.globalMessageTip("数据错误，请刷新页面", 3);
      }
    },
    copyContent(text: string) {
      if (window.navigator.clipboard) {
        window.navigator.clipboard.writeText(text);
        this.globalMessageTip("复制成功", 0);
      } else {
        this.globalMessageTip("复制失败, 请使用https协议访问", 3);
      }
    },
    getVsResult(result: number) {
      switch (result) {
        case 0:
          return "无意义";
        case 1:
          return "1P逃跑";
        case 2:
          return "2P逃跑";
        case 3:
          return "1P赢";
        case 4:
          return "2P赢";
        case 5:
          return "平局";
        default:
          return "未知";
      }
    },
    getCoopResult(result: number) {
      switch (result) {
        case 0:
          return "未通关";
        case 1:
          return "通关";
        default:
          return "未知";
      }
    },
    quckGetDate(type: number) {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();

      switch (type) {
        case 1:
          //获取今天日期
          break;
        case 2:
          // 获取昨天日期
          date.setDate(date.getDate() - 1);
          year = date.getFullYear();
          month = date.getMonth() + 1;
          day = date.getDate();
          break;
        case 3:
          // 获取最近7天日期
          date.setDate(date.getDate() - 7);
          year = date.getFullYear();
          month = date.getMonth() + 1;
          day = date.getDate();
          break;
        case 4:
          // 获取最近一个月日期
          date.setDate(date.getDate() - 30);
          year = date.getFullYear();
          month = date.getMonth() + 1;
          day = date.getDate();
          break;
        default:
          break;
      }
      return `${year}-${month > 9 ? month : "0" + month}-${
        day < 10 ? "0" + day : day
      }`;
    },
    isVtPhone(phone: string) {
      const vtReg =
        /^((1(6[257]|71)[0-9]{8})|(170[0123564789][0-9]{7})|(10641[0-9]{8}))$/;
      return vtReg.test(phone);
    },
  },
});
