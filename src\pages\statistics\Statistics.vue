<script setup lang="ts">
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { useRoute } from 'vue-router';
import { router } from '../../router';
import { Warning, SwitchButton, User, Menu, Avatar, Promotion, BellFilled, WarningFilled, Histogram, Shop } from '@element-plus/icons-vue';
import crypto from 'crypto-js';
import requset from '../../api';

const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

mainStore.fetchAppChannelList();
mainStore.fetchServerList();

function menuGoRoute(name: string) {
    if (route.name === name) {
        return;
    }
    router.push({
        name: name
    });
}

</script>

<template>
    <el-aside class="admin-aside">
        <el-menu class="admin-menu" :default-active="route.name" text-color="#fff" background-color="#333744"
            v-if="mainStore.queryMenuPermission(mainStore.menuList, 4000)">
            <el-sub-menu index="1" v-if="mainStore.queryMenuPermission(mainStore.menuList, 4100)">
                <template #title>
                    <el-icon>
                        <Histogram />
                    </el-icon>
                    <span>用户统计</span>
                </template>
                <el-menu-item index="userActive" @click="menuGoRoute('userActive')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 4101)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>新增&活跃</span>
                </el-menu-item>
            </el-sub-menu>
            <el-sub-menu index="2" v-if="mainStore.queryMenuPermission(mainStore.menuList, 4200)">
                <template #title>
                    <el-icon>
                        <Shop />
                    </el-icon>
                    <span>经营数据</span>
                </template>
                <el-menu-item index="rechargeCount" @click="menuGoRoute('rechargeCount')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 4201)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>收入统计</span>
                </el-menu-item>
                <el-menu-item index="gameSales" @click="menuGoRoute('gameSales')"
                    v-if="mainStore.queryMenuPermission(mainStore.menuList, 4202)">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>Game Sales</span>
                </el-menu-item>
                <el-menu-item index="gameList" @click="menuGoRoute('gameList')">
                    <el-icon>
                        <Menu />
                    </el-icon>
                    <span>Mall Games</span>
                </el-menu-item>
            </el-sub-menu>
        </el-menu>
    </el-aside>
    <el-main class="admin-content">
        <router-view v-if="mainStore.checkPermission(4000)"></router-view>
        <h2 v-else>权限不足, 请联系管理员!</h2>
    </el-main>
</template>

<style lang="less" scoped>

</style>