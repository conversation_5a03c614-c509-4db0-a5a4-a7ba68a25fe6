<script setup lang="ts">
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { useRoute } from 'vue-router';
import { router } from '../../router';
import { Warning, SwitchButton, User, Menu, Avatar, Promotion, BellFilled, WarningFilled, GoodsFilled, VideoCameraFilled, Flag, PictureFilled } from '@element-plus/icons-vue';
import crypto from 'crypto-js';
import requset from '../../api';

const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

function menuGoRoute(name: string) {
    if (route.name === name) {
        return;
    }
    router.push({
        name: name
    });
}

</script>

<template>
    <el-aside class="admin-aside">
        <el-menu class="admin-menu" :default-active="route.name" text-color="#fff" background-color="#333744"
            v-if="mainStore.queryMenuPermission(mainStore.menuList, 2000)">
            <!-- <el-sub-menu index="1">
                <template #title>
                    <el-icon>
                        <GoodsFilled />
                    </el-icon>
                    <span>商城管理</span>
                </template>
            </el-sub-menu> -->
            <el-menu-item index="hackCheck" @click="menuGoRoute('hackCheck')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2001)">
                <el-icon>
                    <WarningFilled />
                </el-icon>
                <span>IP检测功能</span>
            </el-menu-item>
            <el-menu-item index="abroadUnlock" @click="menuGoRoute('abroadUnlock')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2002)">
                <el-icon>
                    <Avatar />
                </el-icon>
                <span>海外用户解锁</span>
            </el-menu-item>
            <el-menu-item index="homeSav" @click="menuGoRoute('homeSav')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2003)">
                <el-icon>
                    <VideoCameraFilled />
                </el-icon>
                <span>平台推荐录像</span>
            </el-menu-item>
            <el-menu-item index="gameSav" @click="menuGoRoute('gameSav')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2004)">
                <el-icon>
                    <VideoCameraFilled />
                </el-icon>
                <span>游戏推荐录像</span>
            </el-menu-item>
            <el-menu-item index="challenge" @click="menuGoRoute('challenge')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2005)">
                <el-icon>
                    <Flag />
                </el-icon>
                <span>挑战房管理</span>
            </el-menu-item>
            <!-- <el-menu-item index="homeBanner" @click="menuGoRoute('homeBanner')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2006)">
                <el-icon>
                    <PictureFilled />
                </el-icon>
                <span>平台海报管理</span>
            </el-menu-item>
            <el-menu-item index="gameBanner" @click="menuGoRoute('homeBanner')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2007)">
                <el-icon>
                    <PictureFilled />
                </el-icon>
                <span>游戏海报管理</span>
            </el-menu-item>
            <el-menu-item index="liveManage" @click="menuGoRoute('liveManage')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 2008)">
                <el-icon>
                    <PictureFilled />
                </el-icon>
                <span>直播间管理</span>
            </el-menu-item> -->
        </el-menu>
    </el-aside>
    <el-main class="admin-content">
        <router-view v-if="mainStore.queryMenuPermission(mainStore.menuList, 2000)"></router-view>
        <h2 v-else>权限不足, 请联系管理员!</h2>
    </el-main>
</template>

<style lang="less" scoped>

</style>