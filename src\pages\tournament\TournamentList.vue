<script setup lang="ts">
import { ArrowRight, House, UploadFilled, Picture } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { router } from '../../router';
import { useTournamentStore } from '../../store/tournament';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { ElMessageBox, genFileId } from 'element-plus';
import { TournamentItem } from '../../interface';

const mainStore = useMainStore();
const tournamentStore = useTournamentStore();
const loading = ref(false);
const addTournamentDialog = ref(false);
const editTournamentDialog = ref(false);

const tournament = reactive({
    data: [] as Array<TournamentItem>
});

const addTournamentBtnLock = ref(true);
const addTournamentForm = reactive({
    challonge_str: '',
    title: '',
    game_id: 0,
    banner_1: '',
    description: '',
    state: 0,
    is_show: 1
});
const editTournamentBtnLock = ref(true);
const editTournamentForm = reactive({
    id: 0,
    challonge_str: '',
    title: '',
    game_id: 0,
    banner_1: '',
    description: '',
    state: 0,
    is_show: 0
});
const addTournamentRuleFormRef = ref<FormInstance>();
const addTournamentFormRules = reactive<FormRules>({
    challonge_str: [
        {
            required: true, message: '请输入第三方ID', trigger: 'blur'
        }
    ],
    game_id: [
        {
            required: true, message: '请选择游戏', trigger: 'blur'
        }
    ],
    title: [
        {
            required: true, message: '请输入赛事标题', trigger: 'blur'
        }
    ],
    banner_1: [
        {
            required: true, message: '请上传海报', trigger: 'blur'
        }
    ],
    state: [
        {
            required: true, message: '请选择赛事状态', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
});

const editTournamentRuleFormRef = ref<FormInstance>();
const editTournamentFormRules = reactive<FormRules>({
    challonge_str: [
        {
            required: true, message: '请输入第三方ID', trigger: 'blur'
        }
    ],
    game_id: [
        {
            required: true, message: '请选择游戏', trigger: 'blur'
        }
    ],
    title: [
        {
            required: true, message: '请输入赛事标题', trigger: 'blur'
        }
    ],
    banner_1: [
        {
            required: true, message: '请上传海报', trigger: 'blur'
        }
    ],
    state: [
        {
            required: true, message: '请选择赛事状态', trigger: 'blur'
        }
    ],
    is_show: [
        {
            required: true, message: '请选择是否展示', trigger: 'blur'
        }
    ],
});
const addUploadRef = ref<UploadInstance>();
const editUploadRef = ref<UploadInstance>();

getTournamentList();

async function getTournamentList() {
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const token = crypto
        .SHA1(`${userID}${authorityID}${requset.ADMIN_TOKEN_KEY}`)
        .toString();
    if (userID && identityToken) {
        loading.value = true;
        await requset.getTournamentList({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            token: token,
        }).then(res => {
            if (res.code === 0) {
                if (res.data && res.data.tournamentList) {
                    tournament.data = res.data.tournamentList;
                }
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        loading.value = false;
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}

function addTournament() {
    addTournamentDialog.value = true;
}

async function addTournamentSubmit(formEl: FormInstance | undefined) {
    if (!addTournamentBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (addTournamentForm.title.length > 128) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (!addTournamentForm.banner_1) {
                return mainStore.globalMessageTip('请上传比赛海报', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${addTournamentForm.challonge_str}${addTournamentForm.game_id}${addTournamentForm.state}${addTournamentForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            addTournamentBtnLock.value = false;
            await requset.addTournament({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                title: addTournamentForm.title,
                challongeStr: addTournamentForm.challonge_str,
                banner1: addTournamentForm.banner_1,
                description: addTournamentForm.description,
                gameID: addTournamentForm.game_id,
                state: addTournamentForm.state,
                isShow: addTournamentForm.is_show,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('添加成功!', 0);
                    addTournamentDialog.value = false;
                    addTournamentForm.title = '';
                    addTournamentForm.challonge_str = '';
                    addTournamentForm.description = '';
                    addTournamentForm.banner_1 = '';
                    addTournamentForm.game_id = 0;
                    addTournamentForm.state = 0;
                    addTournamentForm.is_show = 0;
                    addUploadRef.value?.clearFiles();
                    getTournamentList();
                } else {
                    mainStore.dealResponseErrInfo(res.code);
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            addTournamentBtnLock.value = true;
        } else {
            return;
        }
    });
}

function editTournament(item: TournamentItem) {
    editUploadRef.value?.clearFiles();
    editTournamentForm.game_id = item.game_id;
    editTournamentForm.id = item.id;
    editTournamentForm.banner_1 = item.banner;
    editTournamentForm.title = item.title;
    editTournamentForm.description = item.description;
    editTournamentForm.state = item.state;
    editTournamentForm.challonge_str = item.challonge_str;
    editTournamentForm.is_show = item.is_show;

    editTournamentDialog.value = true;
}

async function editTournamentSubmit(formEl: FormInstance | undefined) {
    if (!editTournamentBtnLock.value) {
        return;
    }
    if (!formEl) {
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (editTournamentForm.title.length > 128) {
                return mainStore.globalMessageTip('标题过长', 3);
            }
            if (!editTournamentForm.banner_1) {
                return mainStore.globalMessageTip('请上传比赛海报', 3);
            }

            const userID = mainStore.userInfo.user_id;
            const identityToken = mainStore.identityToken;
            const authorityID = 10102;
            const token = crypto
                .SHA1(`${userID}${authorityID}${editTournamentForm.id}${editTournamentForm.challonge_str}${editTournamentForm.game_id}${editTournamentForm.state}${editTournamentForm.is_show}${requset.ADMIN_TOKEN_KEY}`)
                .toString();
            mainStore.globalLoading(true);
            editTournamentBtnLock.value = false;
            await requset.editTournament({
                userID: userID,
                identityToken: identityToken,
                authorityID: authorityID,
                targetID: editTournamentForm.id,
                title: editTournamentForm.title,
                gameID: editTournamentForm.game_id,
                description: editTournamentForm.description,
                banner1: editTournamentForm.banner_1,
                state: editTournamentForm.state,
                challongeStr: editTournamentForm.challonge_str,
                isShow: editTournamentForm.is_show,
                token: token
            }).then((res) => {
                if (res.code === 0) {
                    mainStore.globalMessageTip('修改成功!', 0);
                    editTournamentDialog.value = false;
                    editTournamentForm.id = 0;
                    editTournamentForm.banner_1 = '';
                    editTournamentForm.title = '';
                    editTournamentForm.description = '';
                    editTournamentForm.challonge_str = '';
                    editTournamentForm.game_id = 0;
                    editTournamentForm.state = 0;
                    editTournamentForm.is_show = 1;
                    editUploadRef.value?.clearFiles();
                    getTournamentList();
                } else {
                    if (!mainStore.dealResponseErrInfo(res.code)) {
                        switch (res.code) {
                            case 5:
                                mainStore.globalMessageTip('该赛事不存在!', 3);
                                getTournamentList();
                                break;
                            default:
                                break;
                        }
                    }
                }
            }).catch((err) => {
                mainStore.dealResponseErrInfo(-1);
            });
            mainStore.globalLoading(false);
            editTournamentBtnLock.value = true;
        } else {
            return;
        }
    });
}

async function deleteTournament(id: number) {
    if (!id) {
        return;
    }
    ElMessageBox.confirm(`⚠警告: 确定要删除该赛事? 如非特殊情况请勿进行删除操作`, '删除赛事', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
    }).then(async () => {
        const userID = mainStore.userInfo.user_id;
        const identityToken = mainStore.identityToken;
        const authorityID = 10102;
        const token = crypto
            .SHA1(`${userID}${authorityID}${id}${requset.ADMIN_TOKEN_KEY}`)
            .toString();
        mainStore.globalLoading(true);
        await requset.deleteTournament({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            targetID: id,
            token: token
        }).then((res) => {
            if (res.code === 0) {
                mainStore.globalMessageTip('删除成功!', 0);
                getTournamentList();
            } else {
                if (!mainStore.dealResponseErrInfo(res.code)) {
                    switch (res.code) {
                        case 5:
                            mainStore.globalMessageTip('该赛事不存在!', 3);
                            getTournamentList();
                            break;
                        default:
                            break;
                    }
                }
            }
        }).catch((err) => {
            mainStore.dealResponseErrInfo(-1);
        });
        mainStore.globalLoading(false);
    }).catch(() => {

    });
}

function bannerUploadBefore(file: any) {
    console.log(file);
    if (file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'image/jpeg') {
        mainStore.globalMessageTip('只能上传PNG/JPG类型图片', 3);
        return false;
    }
    if (file.size > 5120000) {
        mainStore.globalMessageTip('文件太大了', 3);
        return false;
    }
}

const addUploadExceed: UploadProps['onExceed'] = (files) => {
    addUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    addUploadRef.value!.handleStart(file);
    addUploadRef.value!.submit();
}

const editUploadExceed: UploadProps['onExceed'] = (files) => {
    editUploadRef.value!.clearFiles();
    const file = files[0] as UploadRawFile;
    file.uid = genFileId();
    editUploadRef.value!.handleStart(file);
    editUploadRef.value!.submit();
}

function bannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                addTournamentForm.banner_1 = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function editBannerUploadSuccess(res: any) {
    console.log(res);
    if (res) {
        if (res.code === 0) {
            if (res.data && res.data.file_url) {
                mainStore.globalMessageTip('上传成功', 0);
                editTournamentForm.banner_1 = res.data.file_url;
            } else {
                mainStore.globalMessageTip("未获得返回文件URL", 3);
            }
        } else {
            if (!mainStore.dealResponseErrInfo(res.code)) {
                mainStore.globalMessageTip(res.message, 3);
            }
        }
    }
}

function bannerUploadError(err: any) {
    mainStore.globalMessageTip(err, 3);
}


function gotoTournamentQuiz(id: number) {
    router.push({
        name: 'tournamentQuiz',
        query: {
            id: id
        }
    });
}

</script>

<template>
    <div class="tournament-list" v-if="mainStore.checkPermission(6001)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'tournament'
                }">
                    赛事中心
                </el-breadcrumb-item>
                <el-breadcrumb-item>比赛列表</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-row>
                        <el-col>
                            <el-space wrap>
                                <el-button type="primary" size="large" @click="addTournament()">添加赛事
                                </el-button>
                                <el-button type="primary" size="large" @click="getTournamentList()">刷新
                                </el-button>
                            </el-space>
                        </el-col>
                    </el-row>
                    <el-table v-loading="loading" :data="tournament.data" border style="width: 100%">
                        <el-table-column prop="id" label="ID">
                            <template #default="scope">
                                <span style="color: #F56C6C;" v-if="scope.row.state === 1">{{ scope.row.id }}</span>
                                <span style="color: #E6A23C;" v-else-if="scope.row.state === 0">{{ scope.row.id }}</span>
                                <span v-else>{{ scope.row.id }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="challonge_str" label="第三方ID" />
                        <el-table-column label="游戏">
                            <template #default="scope">
                                <span>{{ mainStore.getGameName(scope.row.game_id) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="title" label="标题" />
                        <el-table-column label="海报">
                            <template #default="scope">
                                <el-image fit="contain" :src="scope.row.banner">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态">
                            <template #default="scope">
                                <el-text class="mx-1" :type="tournamentStore.tournamentState[scope.row.state].type"
                                    size="large">{{
                                        tournamentStore.tournamentState[scope.row.state].label }}</el-text>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作">
                            <template #default="scope">
                                <el-space wrap>
                                    <el-link type="info" @click="editTournament(scope.row)">编辑</el-link>
                                    <el-link type="primary" @click="gotoTournamentQuiz(scope.row.id)">管理竞猜</el-link>
                                    <el-link type="danger" @click="deleteTournament(scope.row.id)">删除</el-link>
                                </el-space>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-space>
            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
    <el-dialog v-model="addTournamentDialog" title="添加赛事">
        <el-form :model="addTournamentForm" :rules="addTournamentFormRules" ref="addTournamentRuleFormRef"
            class="add-tournament-form" status-icon>
            <el-form-item label="第三方ID" prop="challonge_str" required>
                <el-input placeholder="请输入第三方ID" v-model="addTournamentForm.challonge_str" type="text" maxlength="128"
                    size="large" />
            </el-form-item>
            <el-form-item label="标题" prop="title" required>
                <el-input placeholder="请输入标题" v-model="addTournamentForm.title" type="text" maxlength="128" size="large" />
            </el-form-item>
            <el-form-item label="游戏" prop="game_id" required>
                <el-select v-model="addTournamentForm.game_id" placeholder="选择游戏" size="large">
                    <el-option v-for="item in mainStore.gameList" :key="item.game_id" :label="item.game_name"
                        :value="item.game_id" />
                </el-select>
            </el-form-item>
            <el-form-item label="比赛状态" prop="state" required>
                <el-select v-model="addTournamentForm.state" placeholder="选择比赛状态" size="large">
                    <el-option v-for="item in tournamentStore.tournamentState" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="比赛描述">
                <el-input type="textarea" v-model="addTournamentForm.description" :rows="6" placeholder="请输入比赛描述"
                    size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="比赛海报" required>
                <el-upload class="upload-demo" drag accept=".jpeg, .png, .jpg" :limit="1" :on-exceed="addUploadExceed"
                    ref="addUploadRef" :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :before-upload="bannerUploadBefore" :on-success="bannerUploadSuccess"
                    :on-error="bannerUploadError" size="large">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%">
                    <el-image fit="contain" :src="addTournamentForm.banner_1">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="展示" prop="is_show" required>
                <el-switch v-model="addTournamentForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="addTournamentSubmit(addTournamentRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="addTournamentDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
    <el-dialog v-model="editTournamentDialog" title="编辑赛事">
        <el-form :model="editTournamentForm" :rules="editTournamentFormRules" ref="editTournamentRuleFormRef"
            class="edit-tournament-form" status-icon>
            <el-form-item label="ID" prop="id" required>
                <el-input placeholder="ID" v-model="editTournamentForm.id" type="text" disabled />
            </el-form-item>
            <el-form-item label="第三方ID" prop="challonge_str" required>
                <el-input placeholder="请输入第三方ID" v-model="editTournamentForm.challonge_str" type="text" maxlength="128"
                    size="large" />
            </el-form-item>
            <el-form-item label="标题" prop="title" required>
                <el-input placeholder="请输入标题" v-model="editTournamentForm.title" type="text" maxlength="128" size="large" />
            </el-form-item>
            <el-form-item label="游戏" prop="game_id" required>
                <el-select v-model="editTournamentForm.game_id" placeholder="选择游戏" size="large">
                    <el-option v-for="item in mainStore.gameList" :key="item.game_id" :label="item.game_name"
                        :value="item.game_id" />
                </el-select>
            </el-form-item>
            <el-form-item label="比赛状态" prop="state" required>
                <el-select v-model="editTournamentForm.state" placeholder="选择比赛状态" size="large">
                    <el-option v-for="item in tournamentStore.tournamentState" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="比赛描述">
                <el-input type="textarea" v-model="editTournamentForm.description" :rows="6" placeholder="请输入比赛描述"
                    size="large">
                </el-input>
            </el-form-item>
            <el-form-item label="比赛海报" required>
                <el-upload class="upload-demo" drag accept=".jpeg, .png, .jpg" :limit="1" :on-exceed="editUploadExceed"
                    ref="editUploadRef" :action="requset.uploadFileApi" :data="{
                        userID: mainStore.userInfo.user_id,
                        identityToken: mainStore.identityToken,
                        token: crypto.SHA1(`${mainStore.userInfo.user_id}${requset.ADMIN_TOKEN_KEY}`).toString()
                    }" :before-upload="bannerUploadBefore" :on-success="editBannerUploadSuccess"
                    :on-error="bannerUploadError" size="large">
                    <el-icon class="el-icon--upload">
                        <upload-filled />
                    </el-icon>
                    <div class="el-upload__text">
                        将文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传jpg/png文件
                        </div>
                    </template>
                </el-upload>
                <div class="block" style="width: 100%;">
                    <el-image fit="contain" :src="editTournamentForm.banner_1">
                        <template #error>
                            <div class="image-slot">
                                <el-icon>
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
            </el-form-item>
            <el-form-item label="展示" prop="is_show" required>
                <el-switch v-model="editTournamentForm.is_show" size="large" :active-value="1" :inactive-value="0" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer" style="text-align: center;">
                <el-button type="primary" @click="editTournamentSubmit(editTournamentRuleFormRef)" size="large">确定
                </el-button>
                <el-button @click="editTournamentDialog = false" size="large">取消</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style lang="less" scoped></style>