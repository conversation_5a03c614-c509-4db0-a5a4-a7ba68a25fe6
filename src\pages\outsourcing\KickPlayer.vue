<script setup lang="ts">
import { ArrowRight, House } from '@element-plus/icons-vue';
import requset from '../../api';
import crypto from 'crypto-js';
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { computed, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

const loading = ref(false);

const filterForm = reactive({
    game_series: '' as string | number,
    game_id: '' as string | number,
    server_id: '' as string | number,
    room_id: '' as string | number,
});

const filterFormRules = [
    {

    }
];

const gameList = computed(() => {
    return mainStore.gameList.filter(item => item.series_id === filterForm.game_series);
});

const serverList = computed(() => {
    const arr = mainStore.gameServerList.filter(item => item.game_id === filterForm.game_id);
    if (arr.length > 0) {
        return arr[0].server_list;
    } else {
        return [];
    }
});

function selectGameSeries(val: number) {
    const gameIndex = mainStore.gameList.findIndex(item => item.series_id === val);
    if (gameIndex >= 0) {
        filterForm.game_id = mainStore.gameList[gameIndex].game_id;
    } else {
        filterForm.game_id = '';
    }
    const serverIndex1 = mainStore.gameServerList.findIndex(item => item.game_id === filterForm.game_id);
    if (serverIndex1 >= 0) {
        if (mainStore.gameServerList[serverIndex1].server_list.length > 0) {
            filterForm.server_id = mainStore.gameServerList[serverIndex1].server_list[0].server_id;
        } else {
            filterForm.server_id = '';
        }
    } else {
        filterForm.server_id = '';
    }
}

function selectGame(val: number) {
    const serverIndex1 = mainStore.gameServerList.findIndex(item => item.game_id === val);
    if (serverIndex1 >= 0) {
        if (mainStore.gameServerList[serverIndex1].server_list.length > 0) {
            filterForm.server_id = mainStore.gameServerList[serverIndex1].server_list[0].server_id;
        } else {
            filterForm.server_id = '';
        }
    } else {
        filterForm.server_id = '';
    }
}

async function kickPlayer(type: number) {
    if (filterForm.game_id === '' || filterForm.game_id < 0) {
        return mainStore.globalMessageTip('请选择游戏!', 3);
    }
    if (!filterForm.server_id || filterForm.server_id < 1) {
        return mainStore.globalMessageTip('请选择大区!', 3);
    }
    if (!filterForm.room_id || filterForm.room_id < 1 || filterForm.room_id > 220) {
        return mainStore.globalMessageTip('请输入正确的房间号!', 3);
    }
    const userID = mainStore.userInfo.user_id;
    const identityToken = mainStore.identityToken;
    const authorityID = 10102;
    const gameID = parseInt(String(filterForm.game_id));
    const serverID = parseInt(filterForm.server_id.toString());
    const roomID = parseInt(filterForm.room_id.toString());
    const token = crypto.SHA1(`${userID}${authorityID}${type}${serverID}${gameID}${roomID}${requset.ADMIN_TOKEN_KEY}`).toString();
    if (userID && identityToken) {
        mainStore.globalLoading(true);
        await requset.kickPlayer({
            userID: userID,
            identityToken: identityToken,
            authorityID: authorityID,
            kickType: type,
            gameID: gameID,
            serverID: serverID,
            roomID: roomID,
            token: token
        }).then(res => {
            if (res.code === 0) {
                mainStore.globalMessageTip('处理成功', 0);
            } else {
                mainStore.dealResponseErrInfo(res.code);
            }
        }).catch(err => {
            console.log(err);
        });
        mainStore.globalLoading(false);
    } else {
        mainStore.dealResponseErrInfo(4);
    }
}


</script>

<template>
    <div class="kick-player" v-if="mainStore.checkPermission(1003)">
        <el-space direction="vertical" style="width: 100%" fill>
            <el-breadcrumb :separator-icon="ArrowRight">
                <el-breadcrumb-item :to="{
                    name: 'outsourcing'
                }">
                    外协管理
                </el-breadcrumb-item>
                <el-breadcrumb-item>踢人功能</el-breadcrumb-item>
            </el-breadcrumb>
            <el-card class="content" v-loading="loading">
                <el-space direction="vertical" style="width: 100%" fill>
                    <el-form ref="filterFormRef" :model="filterForm" status-icon :rules="filterFormRules"
                        :label-position="'top'" class="filter-form">
                        <el-form-item label="游戏&分区选择">
                            <el-space wrap>
                                <el-select v-model="filterForm.game_series" placeholder="请选择游戏系列" size="large"
                                    @change="selectGameSeries">
                                    <el-option v-for="(item, index) in mainStore.gameSeries" :key="item.series_id"
                                        :label="item.series_name" :value="item.series_id" />
                                </el-select>
                                <el-select v-model="filterForm.game_id" placeholder="请选择游戏" size="large"
                                    @change="selectGame">
                                    <el-option v-for="(item, index) in gameList" :key="item.game_id"
                                        :label="item.game_name" :value="item.game_id" />
                                </el-select>
                                <el-select v-model="filterForm.server_id" placeholder="请选择大区" size="large">
                                    <el-option v-for="(item, index) in serverList" :key="item.server_id"
                                        :label="item.server_name" :value="item.server_id" />
                                </el-select>
                            </el-space>
                        </el-form-item>
                        <el-form-item label="占房功能">
                            <el-input placeholder="房间号1-200,擂台房201开始" v-model="filterForm.room_id" size="large"
                                maxlength="10" :input-style="{width: 'auto',minWidth: '20%'}" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="kickPlayer(1)" size="large">踢1P
                            </el-button>
                            <el-button type="primary" @click="kickPlayer(2)" size="large">踢2P
                            </el-button>
                            <el-button type="primary" @click="kickPlayer(3)" size="large">踢1P和2P
                            </el-button>
                        </el-form-item>
                    </el-form>
                </el-space>

            </el-card>
        </el-space>
    </div>
    <h2 v-else>权限不足, 请联系管理员!</h2>
</template>

<style lang="less" scoped>

</style>