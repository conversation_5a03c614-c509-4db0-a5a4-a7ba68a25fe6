export interface GetLoginSecretParams {
  countryCode?: string;
  account: string;
  token: string;
}

export interface SendVerifySmsCodeParams {
  smsNo: string;
  secretCode: number;
  token: string;
}

export interface LoginParams {
  account: string;
  password: string;
  // email_no: string;
  // email_code: string;
  token: string;
}

export interface CommonParams {
  userID: number;
  authorityID: number;
  identityToken: string;
  token: string;
}

export interface CompanyIDParams extends CommonParams {
  companyID: number;
}

export interface CorrcetUserParams extends CommonParams {
  targetUserID: number;
}

export interface CorrcetNicknameParams extends CommonParams {
  targetUserID: number;
  targetNickname: string;
  handleType: number;
}

export interface CorrcetAvatarParams extends CommonParams {
  targetAvatarID: number;
  handleType: number;
}

export interface ProcessAllUserParams extends CommonParams {
  targetUserID: string;
}

export interface ProcessAllAvatarParams extends CommonParams {
  targetAvatarID: string;
}

export interface KickPlayerParams extends CommonParams {
  kickType: number;
  gameID: number;
  serverID: number;
  roomID: number;
}

export interface RefundRechargeTicketParams extends CommonParams {
  tradeNo: string;
  ticketNum: number;
  targetUser: number;
}

export interface NicknameReview {
  user_id: number;
  nickname: string;
  flag: number;
}

export interface UserAvatar {
  id: number;
  icon_url: string;
}
export interface AvatarReview extends UserAvatar {
  user_id: number;
  flag: number;
}

export interface GameItem {
  game_id: number;
  game_name: string;
  series_id: number;
}
export interface GameSeries {
  series_id: number;
  series_name: string;
}

export interface Server {
  server_id: number;
  server_name: string;
}

export interface GameServer {
  game_id: number;
  server_list: Array<Server>;
}

export interface ServerRegion {
  server_id: number;
  server_name: string;
}

export interface GetReportParams extends CommonParams {
  gameID: string;
  reportType: number;
}

export interface CancelReportParams extends CommonParams {
  reportID: number;
}

export interface TalkMsg {
  id: number;
  server_id: number;
  game_id: number;
  room_id: number;
  msg: string;
  talk_time: string;
}

export interface ReportItem {
  id: number;
  user_id: number;
  nickname: string;
  bereport_userid: number;
  bereport_nickname: string;
  game_id: number;
  report_type: number;
  report_desc: string;
  report_time: string;
  talk_list: Array<TalkMsg> | null;
  hack_list: Array<HackCheckRecord> | null;
  vs_match_record: Array<VsGameMatch> | null;
  coop_match_record: Array<CoopGameMatch> | null;
}

export interface GetUsersTaskParams extends CommonParams {
  currentPage: number;
}

export interface GetUsersLotteryParams extends CommonParams {
  currentPage: number;
}

export interface GetHackCheckListParams extends CommonParams {
  currentPage: number;
  sortType: number;
  showForbid: number;
}

export interface GetUsersActivityTaskParams extends GetUsersTaskParams {
  activityID: number;
}

export interface UserTaskRecord {
  id: number;
  user_id: number;
  nickname: string;
  task_id: number;
  task_name: string;
  receive_active: number;
  complete_time: string;
}

export interface UserActivityTaskRecord {
  id: number;
  user_id: number;
  nickname: string;
  task_id: number;
  task_name: string;
  reward_name: string;
  reward_num: number;
  complete_time: string;
}

export interface UserLotteryRecord {
  id: number;
  user_id: number;
  nickname: string;
  lottery_name: string;
  reward_id: number;
  reward_name: string;
  reward_icon: string;
  lottery_time: string;
}

export interface UserActivityLotteryRecord {
  id: number;
  user_id: number;
  nickname: string;
  lottery_name: string;
  reward_id: number;
  reward_name: string;
  reward_num: number;
  lottery_time: string;
}

export interface ReportHandleParams extends CommonParams {
  reportID: number;
  handleType: number | string;
  punishType: number;
  punishTime: number;
  conclusion: string;
  allFlag: number;
}

export interface UserSearchParams extends CommonParams {
  currentPage: number;
  target_nickname?: string;
  target_account?: string;
  target_userID?: number;
  find_type: number;
}

export interface TradeNoSearchParams extends CommonParams {
  tradeNo: string;
}

export interface AbroadUserSearch extends CommonParams {
  target_nickname?: string;
  target_account?: string;
  target_userID?: number;
  find_type: number;
}

export interface AddChallengeUserParams extends CommonParams {
  target_account: string;
  target_userID: number;
}

export interface UnfreezeUserParams extends CommonParams {
  target_userid: number;
  unfreezeType: number;
}

export interface BanUserParams extends CommonParams {
  target_userid: number;
  handleType: number;
  punishType: number;
  punishTime: number;
}

export interface StatisticsUserActiveDataParams extends CommonParams {
  countType: number;
  startTime: string;
  endTime: string;
  appChannel: number;
  platForm: number;
}

export interface StatisticsRechargeDataParams
  extends StatisticsUserActiveDataParams {
  settlementType: number;
}

export interface ClearUserScoreParams extends CommonParams {
  targetUserID: number;
  gameID: number;
}

export interface SetUserVerifyModeParams extends CommonParams {
  targetUserID: number;
  verifyMode: number;
}

export interface AddLiveRoomParams extends CommonParams {
  targetUserID: number;
  live_url: string;
}

export interface ClearUserAvatarParams extends CommonParams {
  targetUserID: number;
  targetAvatarID: number;
  handleType: number;
}

export interface SendNoticeParams extends CommonParams {
  msg: string;
}

export interface IpParam extends CommonParams {
  ip: string;
}
export interface UserSearchItem {
  user_id: number;
  vip_level: number;
  account: string;
  nickname: string;
  register_time: string;
  last_login_time: string;
  last_recharge_time: string;
  account_state: string;
}

export interface IpUserSearchItem extends UserSearchItem {
  register_ip: string;
  ip_last_login: string;
  last_login_ip: string;
}

export interface RecentLoginItem {
  id: number;
  dev_type: number;
  login_time: string;
  login_ip: string;
  login_area: string;
}

export interface OldNicknameItem {
  id: number;
  nickname: string;
}

export interface HackCheckRecord {
  id: number;
  ip_desc: string;
  check_time: string;
}

export interface IpQuery {
  query: string;
  fields: string;
  lang: string;
}

export interface IpInfo {
  as: string;
  city: string;
  country: string;
  countryCode: string;
  district: string;
  isp: string;
  mobile: boolean;
  org: string;
  proxy: boolean;
  query: string;
  region: string;
  regionName: string;
  status: string;
}

export interface Forbid {
  id: number;
  nickname: string;
  user_id: number;
  forbid_type: string;
  start_time: string;
  end_time: string;
  handler: string;
}

export interface VsGameInfo {
  game_id: number;
  win: number;
  lose: number;
  draw: number;
  escape: number;
  points: number;
  level: number;
}

export interface CoopGameInfo {
  game_id: number;
  single_tg_num: number;
  multi_tg_num: number;
  points: number;
  level: number;
}

export interface VsGameMatch {
  id: number;
  p1_userid: number;
  p2_userid: number;
  p1_level: number;
  p2_level: number;
  p1_ip: string;
  p2_ip: string;
  p1_win_num: number;
  p2_win_num: number;
  total_num: number;
  vs_result: number;
  match_type: number;
  match_time: string;
}

export interface CoopGameMatch {
  id: number;
  clear: number;
  coin: number;
  score: number;
  match_time: string;
}

export interface ServerNotice {
  id: number;
  msg: string;
  time: string;
}

export interface GameCompanyItem {
  company_id: number;
  company_name: string;
  all_unsettled_revenue: number;
  mall_games: Array<MallGameItem>;
}

export interface MallGameItem {
  game_id: number;
  game_name: string;
  game_main_icon: string;
  launch_time: string;
  price: number;
  ratio: number;
  sales_quantity: number;
  total_revenue: number;
  unsettled_revenue: number;
}

export interface AdminMenu {
  menu_id: number;
  menu_name: string;
  path_name: string;
  extends: number;
  children: Array<AdminMenu> | null | undefined;
}

export interface AbroadUserState {
  user_id: number;
  account: string;
  nickname: string;
  move_state_1: number;
  move_state_2: number;
}

export interface UserChinaIdInfo {
  user_id: number;
  account: string;
  nickname: string;
  china_id: string;
  china_name: string;
}

export interface TradeNoItem {
  id: number;
  pay_id: number;
  trade_no: string;
  currency: string;
  pay_channel: string;
  price: number;
  tickets: number;
  lj_tickets: number;
  app_channel: number;
  isnew: number;
  time: string;
  refund_flag: number;
  refund_money: number;
  full_refund: number;
}

export interface UserTicketInfo {
  user_id: number;
  account: string;
  nickname: string;
  ticket: number;
  refund_money: number;
  trade_enough: number;
  tradeList: Array<TradeNoItem>;
}

export interface UserNobleMemberInfo extends UserTicketInfo {
  noble_id: number;
  noble_left_day: number;
  noble_name: string;
  noble_expire_time: string;
  member_expire_time: string;
  member_left_day: number;
  noble_refund_money: number;
  member_refund_money: number;
}

export interface HackCheckItem {
  id: number;
  user_id: number;
  nickname: string;
  check_time: string;
  check_num: number;
  account_state: string;
  ban_flag: number;
  forbid_flag: number;
}

export interface UserTradeInfo {
  id: number;
  user_id: number;
  nickname: string;
  account: string;
  left_ticket: number;
  left_coin: number;
  pay_id: number;
  trade_no: string;
  currency: string;
  pay_channel: string;
  price: number;
  tickets: number;
  app_channel: number;
  state: number;
  time: string;
  refund_flag: number;
}

export interface UserBuyGoodsRecord {
  id: number;
  goods_id: number;
  goods_name: string;
  buy_time: string;
  otype: number;
  currency: number;
  pay_price: number;
  num: number;
}

export interface SettlementRecord {
  id: number;
  amount: number;
  time: string;
}

export interface UserBuyGameRecordItem {
  id: number;
  user_id: number;
  chip_id: string;
  channel: number;
  game_id: number;
  pay_price: number;
  buy_time: string;
}

export interface AppChannel {
  channel_id: number;
  channel_name: string;
}

export interface FormSelectType {
  label: string;
  value: number;
}

export interface ChallengeUser {
  nickname: string;
  user_id: number;
  last_login: string;
}

export interface HomePageRecSavItem {
  id: number;
  title: string;
  icon_url_1: string;
  icon_url_2: string;
  sav_game_id: number;
  sav_ids: string;
  update_time: string;
  currency: number;
  price: number;
  origin_price: number;
  is_show: number;
}

export interface GamePageRecSavItem {
  id: number;
  game_id: number;
  p1_id: number;
  p2_id: number;
  p1_name: string;
  p2_name: string;
  p1_win_num: number;
  p2_win_num: number;
  sav_ids: string;
  update_time: string;
  currency: number;
  price: number;
  origin_price: number;
  is_show: number;
}

export interface AddHomeSavParams extends CommonParams {
  title: string;
  iconUrl1: string;
  iconUrl2: string;
  gameID: number;
  savIds: string;
  currency: number;
  price: number;
  origin_price: number;
  is_show: number;
}

export interface AddGameSavParams extends CommonParams {
  gameID: number;
  p1ID: number;
  p2ID: number;
  p1WinNum: number;
  p2WinNum: number;
  savIds: string;
  currency: number;
  price: number;
  origin_price: number;
  is_show: number;
}

export interface EditHomeSavParams extends AddHomeSavParams {
  targetID: number;
}

export interface EditGameSavParams extends CommonParams {
  targetID: number;
  p1ID: number;
  p2ID: number;
  p1WinNum: number;
  p2WinNum: number;
  savIds: string;
  currency: number;
  price: number;
  origin_price: number;
  is_show: number;
}

export interface DeleteHomeSavParams extends CommonParams {
  targetID: number;
}

export interface SortHomeSavParams extends CommonParams {
  targetID: number;
  sortType: number;
}

export interface GetGameSavParams extends CommonParams {
  gameID: number;
}

export interface GetUserBuyGameRecordParams extends CommonParams {
  companyID: number;
  gameID: number;
  currentPage: number;
}

export interface QueryBatchIpParams extends CommonParams {
  ipList: string;
}

export interface HomePageBannerItem {
  id: number;
  title: string;
  goto_type: number;
  icon_url_1: string;
  icon_url_2: string;
  goto_url_1: string;
  goto_url_2: string;
  sav_game_id: number;
  sav_ids: string;
  start_time: string;
  end_time: string;
  show_sort: number;
  update_time: string;
  state: number;
}

export interface AddHomeBannerParams extends CommonParams {
  title: string;
  gotoType: number;
  iconUrl1: string;
  iconUrl2: string;
  gotoUrl1: string;
  gotoUrl2: string;
  savGameID: number;
  savIds: string;
  startTime: string;
  endTime: string;
}

export interface EditHomeBannerParams extends AddHomeBannerParams {
  targetID: number;
}

export interface UserLiveItem {
  user_id: number;
  nickname: string;
  type: number;
  icon_url: string;
  url: string;
  update_time: string;
  handler: string;
  level: number;
  state: number;
}

export interface AddUserLiveParams extends CommonParams {
  targetUserID: number;
  liveType: number;
  liveLevel: number;
  liveState: number;
  iconUrl: string;
  liveUrl: string;
}

export interface TournamentItem {
  id: number;
  challonge_str: string;
  game_id: number;
  title: string;
  banner: string;
  description: string;
  state: number;
  is_show: number;
}

export interface AddTournamentParams extends CommonParams {
  challongeStr: string;
  title: string;
  gameID: number;
  state: number;
  isShow: number;
  banner1: string;
  description: string;
}

export interface EditTournamentParams extends AddTournamentParams {
  targetID: number;
}

export interface TournamentQuizItem {
  quiz_id: number;
  competition_id: number;
  quiz_title: string;
  quiz_content: string;
  start_time: string;
  end_time: string;
  quiz_state: number;
  stage: string;
  win_option: number;
  big_winner: string;
  big_winner_id: number;
  max_win_money: number;
  is_show: number;
  options: Array<TournamentQuizOptionItem>;
}

export interface TournamentQuizOptionForm {
  id: number;
  option_name: string;
  is_show: number;
}

export interface TournamentQuizOptionItem {
  option_id: number;
  quiz_id: number;
  option_name: string;
  is_show: number;
}

export interface GetTournamentQuizParams extends CommonParams {
  competitionID: number;
}

export interface AddTournamentQuizParams extends CommonParams {
  competitionID: number;
  title: string;
  content: string;
  stage: string;
  startTime: string;
  endTime: string;
  options: Array<TournamentQuizOptionForm>;
  isShow: number;
}

export interface EditTournamentQuizParams extends CommonParams {
  quizID: number;
  title: string;
  content: string;
  stage: string;
  startTime: string;
  endTime: string;
  isShow: number;
}

export interface AddTournamentQuizOptionParams extends CommonParams {
  quizID: number;
  optionName: string;
}

export interface EditTournamentQuizOptionParams extends CommonParams {
  quizID: number;
  optionID: number;
  optionName: string;
}

export interface DeleteTournamentQuizOptionParams extends CommonParams {
  quizID: number;
  optionID: number;
}

export interface ClearUserTicketParams extends CommonParams {
  targetUserID: number;
  refundTradeIds: string;
}
