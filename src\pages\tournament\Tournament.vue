<script setup lang="ts">
import { useMainStore } from '../../store';
import { useOutsourceingStore } from '../../store/outsourcing';
import { useRoute } from 'vue-router';
import { router } from '../../router';
import { Warning, SwitchButton, User, Menu, Avatar, Promotion, BellFilled, WarningFilled, GoodsFilled, VideoCameraFilled, Flag, PictureFilled, WalletFilled } from '@element-plus/icons-vue';
import crypto from 'crypto-js';
import requset from '../../api';

const route = useRoute();
const mainStore = useMainStore();
const outsourcingStore = useOutsourceingStore();

function menuGoRoute(name: string) {
    if (route.name === name) {
        return;
    }
    router.push({
        name: name
    });
}

</script>
<template>
    <el-aside class="admin-aside">
        <el-menu class="admin-menu" :default-active="route.name" text-color="#fff" background-color="#333744"
            v-if="mainStore.queryMenuPermission(mainStore.menuList, 6000)">
            <el-menu-item index="tournaments" @click="menuGoRoute('tournaments')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 6001)">
                <el-icon>
                    <Flag />
                </el-icon>
                <span>比赛列表</span>
            </el-menu-item>
            <el-menu-item index="tournamentQuiz" @click="menuGoRoute('tournamentQuiz')"
                v-if="mainStore.queryMenuPermission(mainStore.menuList, 6002)">
                <el-icon>
                    <WalletFilled />
                </el-icon>
                <span>竞猜管理</span>
            </el-menu-item>
        </el-menu>
    </el-aside>
    <el-main class="admin-content">
        <router-view v-if="mainStore.queryMenuPermission(mainStore.menuList, 6000)"></router-view>
        <h2 v-else>权限不足, 请联系管理员!</h2>
    </el-main>
</template>

<style lang="less" scoped></style>